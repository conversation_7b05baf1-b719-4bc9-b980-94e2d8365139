"""Tests for the ProjectGenerator class."""

import pytest
import tempfile
import shutil
import json
from pathlib import Path
from unittest.mock import MagicMock, patch

from core.project_generator import (
    ProjectGenerator, ProjectGenerationRequest, ProjectStructure,
    ProjectType, Framework
)
from core.file_manager import <PERSON><PERSON>anager
from core.ai_integration import GeminiIntegration

class TestProjectGenerator:
    """Test cases for ProjectGenerator."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.file_manager = FileManager(self.temp_dir)
        
        # Mock AI integration
        self.mock_ai = MagicMock(spec=GeminiIntegration)
        self.mock_ai.model = MagicMock()
        
        self.project_generator = ProjectGenerator(self.file_manager, self.mock_ai)
        
    def teardown_method(self):
        """Clean up test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_project_generator_initialization(self):
        """Test ProjectGenerator initialization."""
        assert self.project_generator.file_manager == self.file_manager
        assert self.project_generator.ai_integration == self.mock_ai
        assert isinstance(self.project_generator.project_templates, dict)
        assert isinstance(self.project_generator.framework_patterns, dict)
    
    def test_fallback_analysis(self):
        """Test fallback analysis when AI parsing fails."""
        # Test web app detection
        analysis = self.project_generator._fallback_analysis("Create a web application")
        assert analysis['project_type'] == 'web_app'
        assert analysis['language'] == 'python'
        
        # Test React detection
        analysis = self.project_generator._fallback_analysis("Build a React app")
        assert analysis['framework'] == 'react'
        
        # Test Flask detection
        analysis = self.project_generator._fallback_analysis("Flask API server")
        assert analysis['framework'] == 'flask'
        assert analysis['project_type'] == 'api'
    
    @patch('core.project_generator.json.loads')
    def test_analyze_project_requirements_success(self, mock_json_loads):
        """Test successful project requirements analysis."""
        # Mock AI response
        mock_response = MagicMock()
        mock_response.text = '''
        {
            "project_type": "web_app",
            "framework": "react",
            "language": "javascript",
            "features": ["routing", "authentication"],
            "complexity": "medium",
            "project_name": "test_app"
        }
        '''
        self.mock_ai.model.generate_content.return_value = mock_response
        
        # Mock JSON parsing
        mock_json_loads.return_value = {
            "project_type": "web_app",
            "framework": "react",
            "language": "javascript",
            "features": ["routing", "authentication"],
            "complexity": "medium",
            "project_name": "test_app"
        }
        
        request = ProjectGenerationRequest(prompt="Create a React web app")
        analysis = self.project_generator._analyze_project_requirements(request)
        
        assert analysis['project_type'] == 'web_app'
        assert analysis['framework'] == 'react'
        assert analysis['language'] == 'javascript'
        assert 'routing' in analysis['features']
    
    def test_analyze_project_requirements_fallback(self):
        """Test project requirements analysis with fallback."""
        # Mock AI to raise exception
        self.mock_ai.model.generate_content.side_effect = Exception("AI Error")
        
        request = ProjectGenerationRequest(prompt="Create a Flask API")
        analysis = self.project_generator._analyze_project_requirements(request)
        
        # Should use fallback analysis
        assert analysis['project_type'] == 'api'
        assert analysis['framework'] == 'flask'
        assert analysis['language'] == 'python'
    
    def test_generate_project_structure(self):
        """Test project structure generation."""
        analysis = {
            'project_type': 'web_app',
            'framework': 'flask',
            'language': 'python',
            'features': ['authentication'],
            'complexity': 'simple',
            'project_name': 'test_flask_app',
            'description': 'A test Flask application'
        }
        
        request = ProjectGenerationRequest(prompt="Test prompt")
        structure = self.project_generator._generate_project_structure(analysis, request)
        
        assert isinstance(structure, ProjectStructure)
        assert structure.name == 'test_flask_app'
        assert structure.project_type == ProjectType.WEB_APP
        assert structure.framework == Framework.FLASK
        assert structure.language == 'python'
        assert 'pip' in structure.dependencies
    
    def test_generate_common_files(self):
        """Test generation of common project files."""
        structure = ProjectStructure(
            name="test_project",
            description="Test project",
            project_type=ProjectType.WEB_APP,
            framework=Framework.FLASK,
            language="python",
            files=[],
            directories=[],
            dependencies={'pip': ['flask', 'pytest']},
            setup_instructions=[],
            estimated_complexity="simple"
        )
        
        analysis = {
            'features': ['basic_functionality'],
            'project_name': 'test_project',
            'description': 'Test project'
        }
        
        self.project_generator._generate_common_files(structure, analysis)
        
        # Check that common files were generated
        file_paths = [f.path for f in structure.files]
        assert 'README.md' in file_paths
        assert '.gitignore' in file_paths
        assert 'requirements.txt' in file_paths
        
        # Check README content
        readme_file = next(f for f in structure.files if f.path == 'README.md')
        assert 'test_project' in readme_file.content
        assert 'Test project' in readme_file.content
        
        # Check requirements.txt content
        req_file = next(f for f in structure.files if f.path == 'requirements.txt')
        assert 'flask' in req_file.content
        assert 'pytest' in req_file.content
    
    def test_generate_flask_files(self):
        """Test Flask-specific file generation."""
        structure = ProjectStructure(
            name="flask_app",
            description="Flask application",
            project_type=ProjectType.WEB_APP,
            framework=Framework.FLASK,
            language="python",
            files=[],
            directories=[],
            dependencies={},
            setup_instructions=[],
            estimated_complexity="simple"
        )
        
        analysis = {
            'project_name': 'flask_app',
            'description': 'Flask application',
            'features': ['basic_web_app']
        }
        
        self.project_generator._generate_flask_files(structure, analysis)
        
        # Check that Flask files were generated
        file_paths = [f.path for f in structure.files]
        assert 'app.py' in file_paths
        assert 'templates/index.html' in file_paths
        assert 'static/style.css' in file_paths
        
        # Check app.py content
        app_file = next(f for f in structure.files if f.path == 'app.py')
        assert 'from flask import Flask' in app_file.content
        assert '@app.route' in app_file.content
    
    def test_generate_react_files(self):
        """Test React-specific file generation."""
        structure = ProjectStructure(
            name="react_app",
            description="React application",
            project_type=ProjectType.WEB_APP,
            framework=Framework.REACT,
            language="javascript",
            files=[],
            directories=[],
            dependencies={},
            setup_instructions=[],
            estimated_complexity="simple"
        )
        
        analysis = {
            'project_name': 'react_app',
            'description': 'React application',
            'features': ['spa', 'components']
        }
        
        self.project_generator._generate_react_files(structure, analysis)
        
        # Check that React files were generated
        file_paths = [f.path for f in structure.files]
        assert 'src/App.jsx' in file_paths
        assert 'src/index.js' in file_paths
        assert 'public/index.html' in file_paths
        assert 'src/App.css' in file_paths
        
        # Check App.jsx content
        app_file = next(f for f in structure.files if f.path == 'src/App.jsx')
        assert 'import React from' in app_file.content
        assert 'function App()' in app_file.content
    
    def test_generate_package_json(self):
        """Test package.json generation."""
        structure = ProjectStructure(
            name="node_app",
            description="Node.js application",
            project_type=ProjectType.WEB_APP,
            framework=Framework.EXPRESS,
            language="javascript",
            files=[],
            directories=[],
            dependencies={'npm': ['express', 'cors']},
            setup_instructions=[],
            estimated_complexity="simple"
        )
        
        analysis = {
            'features': ['api', 'cors'],
            'project_name': 'node_app',
            'description': 'Node.js application'
        }
        
        package_json_content = self.project_generator._generate_package_json(structure, analysis)
        package_data = json.loads(package_json_content)
        
        assert package_data['name'] == 'node-app'
        assert package_data['description'] == 'Node.js application'
        assert 'express' in package_data['dependencies']
        assert 'cors' in package_data['dependencies']
        assert 'jest' in package_data['devDependencies']
    
    def test_generate_setup_instructions(self):
        """Test setup instructions generation."""
        # Python project
        analysis = {'language': 'python'}
        dependencies = {'pip': ['flask', 'pytest']}
        
        instructions = self.project_generator._generate_setup_instructions(analysis, dependencies)
        
        assert any('virtual environment' in inst for inst in instructions)
        assert any('pip install' in inst for inst in instructions)
        
        # Node.js project
        analysis = {'language': 'javascript'}
        dependencies = {'npm': ['express', 'jest']}
        
        instructions = self.project_generator._generate_setup_instructions(analysis, dependencies)
        
        assert any('npm install' in inst for inst in instructions)
        assert any('npm start' in inst for inst in instructions)
    
    def test_full_project_generation_workflow(self):
        """Test complete project generation workflow."""
        # Mock AI response for analysis
        mock_response = MagicMock()
        mock_response.text = '''
        {
            "project_type": "web_app",
            "framework": "flask",
            "language": "python",
            "features": ["basic_web_app"],
            "complexity": "simple",
            "project_name": "test_app",
            "description": "A test Flask application"
        }
        '''
        self.mock_ai.model.generate_content.return_value = mock_response
        
        request = ProjectGenerationRequest(
            prompt="Create a simple Flask web application",
            project_name="test_app",
            include_tests=True,
            include_docs=True
        )
        
        with patch('core.project_generator.json.loads') as mock_json:
            mock_json.return_value = {
                "project_type": "web_app",
                "framework": "flask",
                "language": "python",
                "features": ["basic_web_app"],
                "complexity": "simple",
                "project_name": "test_app",
                "description": "A test Flask application"
            }
            
            structure = self.project_generator.generate_project(request)
        
        # Verify project structure
        assert structure.name == "test_app"
        assert structure.project_type == ProjectType.WEB_APP
        assert structure.framework == Framework.FLASK
        assert len(structure.files) > 0
        
        # Verify essential files are present
        file_paths = [f.path for f in structure.files]
        assert 'README.md' in file_paths
        assert 'requirements.txt' in file_paths
        assert '.gitignore' in file_paths
        assert 'app.py' in file_paths
    
    def test_project_generation_error_handling(self):
        """Test error handling in project generation."""
        # Mock AI to raise exception
        self.mock_ai.model.generate_content.side_effect = Exception("AI Service Error")
        
        request = ProjectGenerationRequest(prompt="Create an app")
        
        # Should not raise exception, should use fallback
        structure = self.project_generator.generate_project(request)
        assert isinstance(structure, ProjectStructure)
        assert structure.name == "generated_project"  # Default fallback name

if __name__ == "__main__":
    pytest.main([__file__])
