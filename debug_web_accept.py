#!/usr/bin/env python3
"""Debug script to test web interface accept button issue."""

import streamlit as st
from core.analysis_engine import AnalysisEngine

def debug_web_accept():
    """Debug the web interface accept button."""
    print("🔍 Debugging web interface accept button...")
    
    try:
        # Initialize engine like web interface does
        engine = AnalysisEngine()
        print("✅ AnalysisEngine initialized")
        
        # Generate a project like web interface does
        print("\n🚀 Generating project (simulating web interface)...")
        session_id = engine.generate_project(
            prompt="Create a simple React calculator app",
            project_name="Calculator App",
            preferred_framework="React",
            preferred_language="JavaScript",
            include_tests=True,
            include_docs=True,
            target_folder="CALC_REACT"  # Same as your screenshot
        )
        
        print(f"✅ Project generated with session ID: {session_id}")
        
        # Check session like web interface does
        session = engine.get_session(session_id)
        if not session:
            print("❌ Session not found!")
            return False
        
        print(f"📋 Session found with {len(session.change_proposals)} proposals")
        
        # Show what the web interface should see
        for i, proposal in enumerate(session.change_proposals):
            print(f"  {i+1}. {proposal.file_diff.file_path}")
            print(f"     Status: {proposal.status}")
            print(f"     Content length: {len(proposal.file_diff.new_content)} chars")
            print(f"     Title: {proposal.title}")
        
        # Check workspace before apply
        workspace_root = engine.file_manager.workspace_root
        calc_folder = workspace_root / "CALC_REACT"
        
        print(f"\n📂 Checking workspace before apply...")
        print(f"   Workspace root: {workspace_root}")
        print(f"   Target folder: {calc_folder}")
        print(f"   Folder exists: {calc_folder.exists()}")
        
        if calc_folder.exists():
            existing_files = list(calc_folder.glob("*"))
            print(f"   Existing files: {len(existing_files)}")
        
        # Apply like web interface does
        print(f"\n📁 Applying project generation (simulating Accept All Files)...")
        
        try:
            success = engine.apply_project_generation(session_id, selected_files=None)
            print(f"✅ Apply result: {success}")
        except Exception as e:
            print(f"❌ Apply failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Check workspace after apply
        print(f"\n📂 Checking workspace after apply...")
        print(f"   Folder exists: {calc_folder.exists()}")
        
        if calc_folder.exists():
            created_files = list(calc_folder.rglob("*"))
            file_count = len([f for f in created_files if f.is_file()])
            print(f"   Created files: {file_count}")
            
            for file_path in created_files:
                if file_path.is_file():
                    rel_path = file_path.relative_to(calc_folder)
                    size = file_path.stat().st_size
                    print(f"     - {rel_path} ({size} bytes)")
        else:
            print("   ❌ Folder was not created!")
        
        # Check proposal statuses after apply
        session_after = engine.get_session(session_id)
        if session_after:
            print(f"\n📋 Proposal statuses after apply:")
            for i, proposal in enumerate(session_after.change_proposals):
                print(f"  {i+1}. {proposal.file_diff.file_path}: {proposal.status}")
        
        return calc_folder.exists() and file_count > 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_diff_manager_apply():
    """Debug the diff manager apply process specifically."""
    print("\n🔧 Debugging diff manager apply process...")
    
    try:
        from core.diff_manager import DiffManager, FileDiff, ChangeProposal
        from core.file_manager import FileManager
        
        file_manager = FileManager()
        diff_manager = DiffManager()
        
        # Create a test proposal exactly like project generation does
        test_content = '''import React from 'react';

function Calculator() {
  return (
    <div className="calculator">
      <h1>Calculator</h1>
      <p>A simple React calculator app</p>
    </div>
  );
}

export default Calculator;
'''
        
        proposal = diff_manager.create_change_proposal(
            title="Create Calculator.jsx",
            description="Main calculator component",
            old_content="",
            new_content=test_content,
            file_path="CALC_REACT/src/Calculator.jsx",
            confidence=0.9,
            reasoning="Generated React component",
            suggestion_type="project_generation"
        )
        
        print(f"✅ Created test proposal: {proposal.id}")
        print(f"📄 File path: {proposal.file_path}")
        print(f"📝 Content length: {len(proposal.file_diff.new_content)} chars")
        print(f"🔄 Status: {proposal.status}")
        
        # Try to accept and apply like the engine does
        print(f"\n🔄 Accepting change...")
        diff_manager.accept_change(proposal.id)
        
        # Check status after accept
        updated_proposal = diff_manager.get_change(proposal.id)
        print(f"📋 Status after accept: {updated_proposal.status}")
        
        print(f"📁 Applying change...")
        diff_manager.apply_change(proposal.id, file_manager)
        
        # Check if file was created
        test_file_path = file_manager.workspace_root / "CALC_REACT" / "src" / "Calculator.jsx"
        print(f"🔍 Checking file: {test_file_path}")
        
        if test_file_path.exists():
            print(f"✅ File created successfully!")
            with open(test_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            print(f"📄 Content length: {len(content)} chars")
            print(f"📄 First 100 chars: {content[:100]}...")
        else:
            print(f"❌ File not created!")
            
            # Check if parent directories exist
            parent_dir = test_file_path.parent
            print(f"📁 Parent dir exists: {parent_dir.exists()}")
            if parent_dir.exists():
                print(f"📁 Parent dir contents: {list(parent_dir.glob('*'))}")
        
        return test_file_path.exists()
        
    except Exception as e:
        print(f"❌ Diff manager error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Debugging Web Interface Accept Button")
    print("=" * 60)
    
    # Test the full workflow
    workflow_success = debug_web_accept()
    
    if not workflow_success:
        print("\n" + "=" * 60)
        print("🔧 Testing diff manager directly...")
        diff_success = debug_diff_manager_apply()
        
        if diff_success:
            print("\n✅ Diff manager works - issue is in project generation workflow")
        else:
            print("\n❌ Diff manager has issues - this is the root cause")
    else:
        print("\n🎉 Web interface accept functionality is working!")
        
    print("\n" + "=" * 60)
    print("🔍 Summary:")
    print(f"   Full workflow: {'✅ PASS' if workflow_success else '❌ FAIL'}")
    if not workflow_success:
        diff_success = debug_diff_manager_apply() if 'diff_success' not in locals() else diff_success
        print(f"   Diff manager: {'✅ PASS' if diff_success else '❌ FAIL'}")
