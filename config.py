"""Configuration management for the AI Code Assistant."""

import os
from pathlib import Path
from typing import List, Set
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Application configuration class."""
    
    # API Configuration
    GEMINI_API_KEY: str = os.getenv("GEMINI_API_KEY", "")
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")

    # AI Model Selection
    DEFAULT_AI_PROVIDER: str = os.getenv("DEFAULT_AI_PROVIDER", "openai")  # "openai" or "gemini"
    OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "gpt-4")  # Best for code generation
    GEMINI_MODEL: str = os.getenv("GEMINI_MODEL", "gemini-1.5-flash")
    
    # File System Configuration
    WORKSPACE_ROOT: Path = Path(os.getenv("WORKSPACE_ROOT", "./workspace"))
    MAX_FILE_SIZE_MB: int = int(os.getenv("MAX_FILE_SIZE_MB", "10"))
    MAX_FILE_SIZE_BYTES: int = MAX_FILE_SIZE_MB * 1024 * 1024
    
    # Allowed file extensions
    ALLOWED_EXTENSIONS: Set[str] = set(
        ext.strip() for ext in os.getenv(
            "ALLOWED_EXTENSIONS",
            ".py,.js,.jsx,.tsx,.html,.css,.ts,.json,.md,.txt,.yml,.yaml,.xml,.sql,.sh,.bat,.gitignore,.env,.dockerignore,.editorconfig,.vue,.php,.rb,.go,.rs,.java,.c,.cpp,.h,.hpp,.cs,.swift,.kt"
        ).split(",")
    )
    
    # Security Settings
    ENABLE_FILE_RESTRICTIONS: bool = os.getenv("ENABLE_FILE_RESTRICTIONS", "true").lower() == "true"
    ALLOW_HIDDEN_FILES: bool = os.getenv("ALLOW_HIDDEN_FILES", "false").lower() == "true"
    ALLOW_SYSTEM_FILES: bool = os.getenv("ALLOW_SYSTEM_FILES", "false").lower() == "true"
    
    # System directories to avoid (security)
    FORBIDDEN_PATHS: Set[str] = {
        "/etc", "/bin", "/sbin", "/usr/bin", "/usr/sbin", "/var", "/tmp",
        "C:\\Windows", "C:\\Program Files", "C:\\Program Files (x86)",
        "C:\\System32", "C:\\Users\\<USER>