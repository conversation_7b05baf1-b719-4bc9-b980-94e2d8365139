"""Core analysis engine that orchestrates AI analysis and change management."""

import logging
from typing import List, Dict, Optional, Any, Tuple
from pathlib import Path
from dataclasses import dataclass
import time

from .file_manager import FileManager, FileInfo
from .ai_integration import AnalysisType, CodeSuggestion, AnalysisResult
from .ai_manager import AIManager
from .diff_manager import <PERSON>ff<PERSON><PERSON><PERSON>, ChangeProposal
from .project_generator import ProjectGenerator, ProjectGenerationRequest, ProjectStructure

logger = logging.getLogger(__name__)

@dataclass
class AnalysisContext:
    """Context information for code analysis."""
    project_files: List[FileInfo]
    related_files: List[str]
    project_type: Optional[str]
    dependencies: List[str]
    coding_standards: Optional[str]

@dataclass
class AnalysisSession:
    """A complete analysis session."""
    session_id: str
    file_path: str
    analysis_type: AnalysisType
    context: AnalysisContext
    ai_result: AnalysisResult
    change_proposals: List[ChangeProposal]
    created_at: float

class AnalysisEngine:
    """Core engine that orchestrates code analysis and change management."""
    
    def __init__(self, workspace_root: Optional[Path] = None):
        """Initialize the analysis engine."""
        self.file_manager = FileManager(workspace_root)
        self.ai_manager = AIManager()
        self.diff_manager = DiffManager()
        self.project_generator = ProjectGenerator(self.file_manager, self.ai_manager)

        self.analysis_sessions: Dict[str, AnalysisSession] = {}

        logger.info("AnalysisEngine initialized successfully")
    
    def analyze_file(self, file_path: str, analysis_type: AnalysisType = AnalysisType.REVIEW,
                    auto_create_proposals: bool = True) -> AnalysisSession:
        """Analyze a single file and create change proposals."""
        try:
            session_id = f"session_{int(time.time() * 1000)}"
            
            # Read file content
            file_content = self.file_manager.read_file(file_path)
            file_info = self.file_manager.get_file_info(file_path)
            
            # Build analysis context
            context = self._build_analysis_context(file_path, file_info)
            
            # Perform AI analysis using the best available provider
            ai_result = self.ai_manager.analyze_code(
                code=file_content,
                file_path=file_path,
                analysis_type=analysis_type
            )
            
            # Create change proposals if requested
            change_proposals = []
            if auto_create_proposals:
                change_proposals = self._create_proposals_from_suggestions(
                    ai_result.suggestions, file_content, file_path
                )
            
            # Create analysis session
            session = AnalysisSession(
                session_id=session_id,
                file_path=file_path,
                analysis_type=analysis_type,
                context=context,
                ai_result=ai_result,
                change_proposals=change_proposals,
                created_at=time.time()
            )
            
            self.analysis_sessions[session_id] = session
            
            logger.info(f"Completed analysis session {session_id} for {file_path}")
            return session
            
        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
            raise
    
    def analyze_multiple_files(self, file_paths: List[str], 
                              analysis_type: AnalysisType = AnalysisType.REVIEW) -> List[AnalysisSession]:
        """Analyze multiple files."""
        sessions = []
        
        for file_path in file_paths:
            try:
                session = self.analyze_file(file_path, analysis_type)
                sessions.append(session)
            except Exception as e:
                logger.error(f"Error analyzing {file_path}: {e}")
                continue
        
        return sessions
    
    def analyze_project(self, analysis_type: AnalysisType = AnalysisType.REVIEW,
                       file_pattern: str = "*.py") -> List[AnalysisSession]:
        """Analyze all files in the project matching a pattern."""
        try:
            # Get all files matching pattern
            all_files = self.file_manager.list_files(recursive=True)
            target_files = [
                str(f.path) for f in all_files 
                if not f.is_directory and Path(f.name).match(file_pattern)
            ]
            
            logger.info(f"Found {len(target_files)} files matching pattern {file_pattern}")
            
            return self.analyze_multiple_files(target_files, analysis_type)
            
        except Exception as e:
            logger.error(f"Error analyzing project: {e}")
            raise
    
    def _build_analysis_context(self, file_path: str, file_info: FileInfo) -> AnalysisContext:
        """Build context information for analysis."""
        try:
            # Get project files
            project_files = self.file_manager.list_files(recursive=True)
            
            # Find related files (same directory, imports, etc.)
            related_files = self._find_related_files(file_path, project_files)
            
            # Detect project type
            project_type = self._detect_project_type(project_files)
            
            # Extract dependencies
            dependencies = self._extract_dependencies(file_path, project_files)
            
            return AnalysisContext(
                project_files=project_files,
                related_files=related_files,
                project_type=project_type,
                dependencies=dependencies,
                coding_standards=None  # Could be loaded from config
            )
            
        except Exception as e:
            logger.warning(f"Error building analysis context: {e}")
            return AnalysisContext(
                project_files=[],
                related_files=[],
                project_type=None,
                dependencies=[],
                coding_standards=None
            )
    
    def _find_related_files(self, file_path: str, project_files: List[FileInfo]) -> List[str]:
        """Find files related to the target file."""
        related = []
        file_dir = Path(file_path).parent
        
        # Files in same directory
        for file_info in project_files:
            if file_info.is_directory:
                continue
                
            if file_info.path.parent == file_dir:
                related.append(str(file_info.path))
        
        # TODO: Add more sophisticated relationship detection
        # - Import analysis
        # - Class inheritance
        # - Function calls
        
        return related[:10]  # Limit to avoid overwhelming the AI
    
    def _detect_project_type(self, project_files: List[FileInfo]) -> Optional[str]:
        """Detect the type of project based on files present."""
        file_names = {f.name.lower() for f in project_files if not f.is_directory}
        
        # Python projects
        if any(name in file_names for name in ['requirements.txt', 'setup.py', 'pyproject.toml']):
            return "Python"
        
        # JavaScript/Node.js projects
        if 'package.json' in file_names:
            return "JavaScript/Node.js"
        
        # Java projects
        if any(name in file_names for name in ['pom.xml', 'build.gradle']):
            return "Java"
        
        # C# projects
        if any(f.extension == '.csproj' for f in project_files):
            return "C#"
        
        # Go projects
        if 'go.mod' in file_names:
            return "Go"
        
        # Rust projects
        if 'cargo.toml' in file_names:
            return "Rust"
        
        return None
    
    def _extract_dependencies(self, file_path: str, project_files: List[FileInfo]) -> List[str]:
        """Extract project dependencies."""
        dependencies = []
        
        try:
            # Python dependencies
            for file_info in project_files:
                if file_info.name == 'requirements.txt':
                    content = self.file_manager.read_file(str(file_info.path))
                    deps = [line.split('==')[0].split('>=')[0].split('<=')[0].strip() 
                           for line in content.split('\n') 
                           if line.strip() and not line.startswith('#')]
                    dependencies.extend(deps[:20])  # Limit to avoid overwhelming
                    break
            
            # TODO: Add support for other dependency files
            # - package.json for Node.js
            # - pom.xml for Java
            # - go.mod for Go
            # etc.
            
        except Exception as e:
            logger.warning(f"Error extracting dependencies: {e}")
        
        return dependencies
    
    def _format_context_for_ai(self, context: AnalysisContext) -> str:
        """Format analysis context for AI consumption."""
        context_parts = []
        
        if context.project_type:
            context_parts.append(f"Project Type: {context.project_type}")
        
        if context.dependencies:
            deps_str = ", ".join(context.dependencies[:10])
            context_parts.append(f"Dependencies: {deps_str}")
        
        if context.related_files:
            files_str = ", ".join(context.related_files[:5])
            context_parts.append(f"Related Files: {files_str}")
        
        return " | ".join(context_parts)
    
    def _create_proposals_from_suggestions(self, suggestions: List[CodeSuggestion], 
                                         original_content: str, file_path: str) -> List[ChangeProposal]:
        """Create change proposals from AI suggestions."""
        proposals = []
        
        for suggestion in suggestions:
            try:
                # Only create proposals for suggestions with actual code changes
                if (suggestion.suggested_code and 
                    suggestion.suggested_code.strip() != suggestion.original_code.strip()):
                    
                    proposal = self.diff_manager.create_change_proposal(
                        title=suggestion.title,
                        description=suggestion.description,
                        old_content=original_content,
                        new_content=suggestion.suggested_code,
                        file_path=file_path,
                        confidence=suggestion.confidence,
                        reasoning=suggestion.reasoning,
                        suggestion_type=suggestion.type.value
                    )
                    
                    proposals.append(proposal)
                    
            except Exception as e:
                logger.warning(f"Error creating proposal for suggestion '{suggestion.title}': {e}")
                continue
        
        return proposals
    
    def get_session(self, session_id: str) -> Optional[AnalysisSession]:
        """Get an analysis session by ID."""
        return self.analysis_sessions.get(session_id)
    
    def get_recent_sessions(self, limit: int = 10) -> List[AnalysisSession]:
        """Get recent analysis sessions."""
        sessions = list(self.analysis_sessions.values())
        sessions.sort(key=lambda s: s.created_at, reverse=True)
        return sessions[:limit]
    
    def apply_suggestion(self, session_id: str, suggestion_index: int) -> bool:
        """Apply a specific suggestion from an analysis session."""
        try:
            session = self.analysis_sessions.get(session_id)
            if not session:
                raise ValueError(f"Session {session_id} not found")
            
            if suggestion_index >= len(session.change_proposals):
                raise ValueError(f"Invalid suggestion index {suggestion_index}")
            
            proposal = session.change_proposals[suggestion_index]
            
            # Accept and apply the change
            self.diff_manager.accept_change(proposal.id)
            self.diff_manager.apply_change(proposal.id, self.file_manager)
            
            logger.info(f"Applied suggestion {suggestion_index} from session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error applying suggestion: {e}")
            raise
    
    def quick_fix_error(self, file_path: str, error_message: str) -> Optional[str]:
        """Get a quick fix for a specific error."""
        try:
            file_content = self.file_manager.read_file(file_path)
            fixed_code = self.ai_integration.quick_fix(file_content, error_message, file_path)
            
            if fixed_code and fixed_code != file_content:
                # Create a change proposal for the fix
                proposal = self.diff_manager.create_change_proposal(
                    title=f"Quick Fix: {error_message[:50]}...",
                    description=f"Automated fix for error: {error_message}",
                    old_content=file_content,
                    new_content=fixed_code,
                    file_path=file_path,
                    confidence=0.7,
                    reasoning="Automated error fix",
                    suggestion_type="bug_fix"
                )
                
                return proposal.id
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting quick fix: {e}")
            raise
    
    def get_project_summary(self) -> Dict[str, Any]:
        """Get a summary of the project and recent analysis."""
        try:
            all_files = self.file_manager.list_files(recursive=True)
            
            # File statistics
            file_stats = {
                'total_files': len([f for f in all_files if not f.is_directory]),
                'total_directories': len([f for f in all_files if f.is_directory]),
                'file_types': {}
            }
            
            for file_info in all_files:
                if not file_info.is_directory:
                    ext = file_info.extension or 'no_extension'
                    file_stats['file_types'][ext] = file_stats['file_types'].get(ext, 0) + 1
            
            # Analysis statistics
            analysis_stats = {
                'total_sessions': len(self.analysis_sessions),
                'pending_changes': len(self.diff_manager.get_pending_changes()),
                'recent_sessions': len(self.get_recent_sessions(5))
            }
            
            return {
                'project_type': self._detect_project_type(all_files),
                'file_stats': file_stats,
                'analysis_stats': analysis_stats,
                'workspace_root': str(self.file_manager.workspace_root)
            }
            
        except Exception as e:
            logger.error(f"Error getting project summary: {e}")
            return {}

    def generate_project(self, prompt: str, project_name: Optional[str] = None,
                        preferred_framework: Optional[str] = None,
                        preferred_language: Optional[str] = None,
                        include_tests: bool = True,
                        include_docs: bool = True,
                        target_folder: Optional[str] = None) -> str:
        """Generate a complete project from a natural language prompt."""
        try:
            # Determine project folder name
            if target_folder:
                project_folder = target_folder
            elif project_name:
                # Sanitize project name for folder
                project_folder = self._sanitize_folder_name(project_name)
            else:
                project_folder = f"generated_project_{int(time.time())}"

            # Create project generation request
            request = ProjectGenerationRequest(
                prompt=prompt,
                project_name=project_name or project_folder,
                preferred_framework=preferred_framework,  # Pass as string, will be handled in project generator
                preferred_language=preferred_language,
                include_tests=include_tests,
                include_docs=include_docs,
                target_directory=project_folder,
                create_project_folder=True
            )

            # Generate project structure
            project_structure = self.project_generator.generate_project(request)

            # Create change proposals for each file
            session_id = f"project_gen_{int(time.time() * 1000)}"
            change_proposals = []

            for project_file in project_structure.files:
                # Prepend project folder to file path
                file_path = f"{project_folder}/{project_file.path}"

                proposal = self.diff_manager.create_change_proposal(
                    title=f"Create {file_path}",
                    description=project_file.description,
                    old_content="",  # New file
                    new_content=project_file.content,
                    file_path=file_path,
                    confidence=0.9,
                    reasoning=f"Generated as part of {project_structure.name} project",
                    suggestion_type="project_generation"
                )
                change_proposals.append(proposal)

            # Create analysis session for project generation
            session = AnalysisSession(
                session_id=session_id,
                file_path=f"PROJECT:{project_structure.name}",
                analysis_type=AnalysisType.PROJECT_GENERATION,
                context=AnalysisContext(
                    project_files=[],
                    related_files=[],
                    project_type=project_structure.project_type.value if hasattr(project_structure.project_type, 'value') else str(project_structure.project_type),
                    dependencies=project_structure.dependencies.get('pip', []) +
                               project_structure.dependencies.get('npm', []),
                    coding_standards=None
                ),
                ai_result=AnalysisResult(
                    suggestions=[],
                    summary=f"Generated {project_structure.project_type.value if hasattr(project_structure.project_type, 'value') else str(project_structure.project_type)} project with {len(project_structure.files)} files",
                    analysis_time=0.0
                ),
                change_proposals=change_proposals,
                created_at=time.time()
            )

            self.analysis_sessions[session_id] = session

            logger.info(f"Generated project '{project_structure.name}' with {len(project_structure.files)} files")
            return session_id

        except Exception as e:
            logger.error(f"Error generating project: {e}")
            raise

    def get_project_structure_preview(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get a preview of the generated project structure."""
        try:
            session = self.analysis_sessions.get(session_id)
            if not session or session.analysis_type != AnalysisType.PROJECT_GENERATION:
                return None

            # Build structure preview
            files_by_directory = {}
            for proposal in session.change_proposals:
                file_path = proposal.file_diff.file_path
                directory = str(Path(file_path).parent) if Path(file_path).parent != Path('.') else "root"

                if directory not in files_by_directory:
                    files_by_directory[directory] = []

                files_by_directory[directory].append({
                    'name': Path(file_path).name,
                    'path': file_path,
                    'size': len(proposal.file_diff.new_content),
                    'description': proposal.description,
                    'change_id': proposal.id
                })

            return {
                'session_id': session_id,
                'project_name': session.file_path.replace('PROJECT:', ''),
                'total_files': len(session.change_proposals),
                'directories': files_by_directory,
                'summary': session.ai_result.summary,
                'dependencies': session.context.dependencies
            }

        except Exception as e:
            logger.error(f"Error getting project structure preview: {e}")
            return None

    def apply_project_generation(self, session_id: str, selected_files: Optional[List[str]] = None) -> bool:
        """Apply project generation by creating all files."""
        try:
            session = self.analysis_sessions.get(session_id)
            if not session or session.analysis_type != AnalysisType.PROJECT_GENERATION:
                raise ValueError(f"Invalid project generation session: {session_id}")

            # If no specific files selected, apply all
            if selected_files is None:
                proposals_to_apply = session.change_proposals
            else:
                proposals_to_apply = [
                    p for p in session.change_proposals
                    if p.file_diff.file_path in selected_files
                ]

            # Create directories first
            directories_created = set()
            for proposal in proposals_to_apply:
                file_path = Path(proposal.file_diff.file_path)
                if file_path.parent != Path('.'):
                    dir_path = str(file_path.parent)
                    if dir_path not in directories_created:
                        self.file_manager.create_directory(dir_path)
                        directories_created.add(dir_path)

            # Apply each file creation
            applied_count = 0
            for proposal in proposals_to_apply:
                try:
                    # Accept and apply the change
                    self.diff_manager.accept_change(proposal.id)
                    self.diff_manager.apply_change(proposal.id, self.file_manager)
                    applied_count += 1
                except Exception as e:
                    logger.error(f"Error applying file {proposal.file_diff.file_path}: {e}")
                    continue

            logger.info(f"Applied {applied_count}/{len(proposals_to_apply)} files for project generation")
            return applied_count > 0

        except Exception as e:
            logger.error(f"Error applying project generation: {e}")
            raise

    def _sanitize_folder_name(self, name: str) -> str:
        """Sanitize a project name to be a valid folder name."""
        import re
        # Remove invalid characters and replace with underscores
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', name)
        # Remove multiple underscores and trim
        sanitized = re.sub(r'_+', '_', sanitized).strip('_')
        # Ensure it's not empty
        if not sanitized:
            sanitized = f"project_{int(time.time())}"
        return sanitized

    def list_project_folders(self) -> List[Dict[str, Any]]:
        """List all project folders in the workspace."""
        try:
            workspace_files = self.file_manager.list_files(recursive=False)
            project_folders = []

            for file_info in workspace_files:
                if file_info.is_directory:
                    folder_path = str(file_info.path)

                    # Check if it looks like a project folder
                    folder_files = self.file_manager.list_files(directory=folder_path, recursive=False)
                    has_project_files = any(
                        f.name.lower() in ['readme.md', 'main.py', 'app.py', 'index.js', 'package.json', 'requirements.txt']
                        for f in folder_files if not f.is_directory
                    )

                    if has_project_files:
                        # Get folder stats
                        all_files = self.file_manager.list_files(directory=folder_path, recursive=True)
                        file_count = len([f for f in all_files if not f.is_directory])
                        total_size = sum(f.size for f in all_files if not f.is_directory)

                        # Try to detect project type
                        project_type = self._detect_project_type(all_files)

                        project_folders.append({
                            'name': folder_path,
                            'path': folder_path,
                            'file_count': file_count,
                            'total_size': total_size,
                            'project_type': project_type,
                            'last_modified': file_info.last_modified,
                            'has_readme': any(f.name.lower() == 'readme.md' for f in folder_files),
                            'has_requirements': any(f.name.lower() in ['requirements.txt', 'package.json'] for f in folder_files)
                        })

            # Sort by last modified (newest first)
            project_folders.sort(key=lambda x: x['last_modified'], reverse=True)
            return project_folders

        except Exception as e:
            logger.error(f"Error listing project folders: {e}")
            return []

    def delete_project_folder(self, folder_name: str) -> bool:
        """Delete a project folder and all its contents."""
        try:
            import shutil

            folder_path = self.file_manager.workspace_root / folder_name

            if not folder_path.exists():
                raise ValueError(f"Project folder '{folder_name}' does not exist")

            if not folder_path.is_dir():
                raise ValueError(f"'{folder_name}' is not a directory")

            # Safety check - ensure it's within workspace
            try:
                folder_path.resolve().relative_to(self.file_manager.workspace_root.resolve())
            except ValueError:
                raise PermissionError(f"Cannot delete folder outside workspace: {folder_name}")

            # Delete the entire folder
            shutil.rmtree(folder_path)

            logger.info(f"Deleted project folder: {folder_name}")
            return True

        except Exception as e:
            logger.error(f"Error deleting project folder {folder_name}: {e}")
            raise

    def get_project_folder_info(self, folder_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a project folder."""
        try:
            folder_path = self.file_manager.workspace_root / folder_name

            if not folder_path.exists() or not folder_path.is_dir():
                return None

            # Get all files in the project
            all_files = self.file_manager.list_files(directory=folder_name, recursive=True)

            # Categorize files
            code_files = []
            config_files = []
            doc_files = []
            other_files = []

            for file_info in all_files:
                if file_info.is_directory:
                    continue

                ext = file_info.extension.lower()
                name = file_info.name.lower()

                if ext in ['.py', '.js', '.ts', '.java', '.cpp', '.c', '.cs', '.go', '.rs']:
                    code_files.append(file_info)
                elif name in ['readme.md', 'license', 'changelog.md'] or ext in ['.md', '.txt']:
                    doc_files.append(file_info)
                elif name in ['package.json', 'requirements.txt', '.gitignore', 'dockerfile'] or ext in ['.json', '.yml', '.yaml', '.toml']:
                    config_files.append(file_info)
                else:
                    other_files.append(file_info)

            return {
                'name': folder_name,
                'path': folder_name,
                'total_files': len(all_files) - len([f for f in all_files if f.is_directory]),
                'total_directories': len([f for f in all_files if f.is_directory]),
                'total_size': sum(f.size for f in all_files if not f.is_directory),
                'code_files': len(code_files),
                'config_files': len(config_files),
                'doc_files': len(doc_files),
                'other_files': len(other_files),
                'project_type': self._detect_project_type(all_files),
                'last_modified': max(f.last_modified for f in all_files if not f.is_directory) if all_files else 0,
                'files_by_type': {
                    'code': [{'name': f.name, 'path': str(f.path), 'size': f.size} for f in code_files[:10]],
                    'config': [{'name': f.name, 'path': str(f.path), 'size': f.size} for f in config_files],
                    'docs': [{'name': f.name, 'path': str(f.path), 'size': f.size} for f in doc_files],
                    'other': [{'name': f.name, 'path': str(f.path), 'size': f.size} for f in other_files[:5]]
                }
            }

        except Exception as e:
            logger.error(f"Error getting project folder info for {folder_name}: {e}")
            return None

    def _detect_project_type(self, files) -> str:
        """Detect project type based on files present."""
        try:
            file_names = [f.name.lower() for f in files if not f.is_directory]
            file_extensions = [f.extension.lower() for f in files if not f.is_directory and f.extension]

            # Python projects
            if 'requirements.txt' in file_names or 'setup.py' in file_names or 'pyproject.toml' in file_names:
                if 'app.py' in file_names or 'main.py' in file_names:
                    return "Python Application"
                return "Python Project"

            # Node.js projects
            if 'package.json' in file_names:
                if any('react' in name for name in file_names) or '.jsx' in file_extensions:
                    return "React Application"
                elif any('vue' in name for name in file_names) or '.vue' in file_extensions:
                    return "Vue Application"
                elif any('angular' in name for name in file_names) or '.ts' in file_extensions:
                    return "Angular Application"
                else:
                    return "Node.js Project"

            # Web projects
            if 'index.html' in file_names or any('.html' in ext for ext in file_extensions):
                if any('.js' in ext for ext in file_extensions):
                    return "Web Application"
                return "Static Website"

            # Java projects
            if 'pom.xml' in file_names or 'build.gradle' in file_names:
                return "Java Project"

            # C# projects
            if any('.csproj' in name for name in file_names) or any('.sln' in name for name in file_names):
                return "C# Project"

            # Go projects
            if 'go.mod' in file_names:
                return "Go Project"

            # Rust projects
            if 'cargo.toml' in file_names:
                return "Rust Project"

            # Generic based on file extensions
            if '.py' in file_extensions:
                return "Python Project"
            elif '.js' in file_extensions or '.ts' in file_extensions:
                return "JavaScript Project"
            elif '.java' in file_extensions:
                return "Java Project"
            elif '.cpp' in file_extensions or '.c' in file_extensions:
                return "C/C++ Project"
            elif '.cs' in file_extensions:
                return "C# Project"
            elif '.go' in file_extensions:
                return "Go Project"
            elif '.rs' in file_extensions:
                return "Rust Project"

            return "Unknown"

        except Exception as e:
            logger.error(f"Error detecting project type: {e}")
            return "Unknown"
