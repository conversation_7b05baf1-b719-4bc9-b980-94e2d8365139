"""Core analysis engine that orchestrates AI analysis and change management."""

import logging
from typing import List, Dict, Optional, Any, Tuple
from pathlib import Path
from dataclasses import dataclass
import time

from .file_manager import FileManager, FileInfo
from .ai_integration import GeminiIntegration, AnalysisType, CodeSuggestion, AnalysisResult
from .diff_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, ChangeProposal

logger = logging.getLogger(__name__)

@dataclass
class AnalysisContext:
    """Context information for code analysis."""
    project_files: List[FileInfo]
    related_files: List[str]
    project_type: Optional[str]
    dependencies: List[str]
    coding_standards: Optional[str]

@dataclass
class AnalysisSession:
    """A complete analysis session."""
    session_id: str
    file_path: str
    analysis_type: AnalysisType
    context: AnalysisContext
    ai_result: AnalysisResult
    change_proposals: List[ChangeProposal]
    created_at: float

class AnalysisEngine:
    """Core engine that orchestrates code analysis and change management."""
    
    def __init__(self, workspace_root: Optional[Path] = None):
        """Initialize the analysis engine."""
        self.file_manager = FileManager(workspace_root)
        self.ai_integration = GeminiIntegration()
        self.diff_manager = DiffManager()
        
        self.analysis_sessions: Dict[str, AnalysisSession] = {}
        
        logger.info("AnalysisEngine initialized successfully")
    
    def analyze_file(self, file_path: str, analysis_type: AnalysisType = AnalysisType.REVIEW,
                    auto_create_proposals: bool = True) -> AnalysisSession:
        """Analyze a single file and create change proposals."""
        try:
            session_id = f"session_{int(time.time() * 1000)}"
            
            # Read file content
            file_content = self.file_manager.read_file(file_path)
            file_info = self.file_manager.get_file_info(file_path)
            
            # Build analysis context
            context = self._build_analysis_context(file_path, file_info)
            
            # Perform AI analysis
            ai_result = self.ai_integration.analyze_code(
                code=file_content,
                file_path=file_path,
                analysis_type=analysis_type,
                context=self._format_context_for_ai(context)
            )
            
            # Create change proposals if requested
            change_proposals = []
            if auto_create_proposals:
                change_proposals = self._create_proposals_from_suggestions(
                    ai_result.suggestions, file_content, file_path
                )
            
            # Create analysis session
            session = AnalysisSession(
                session_id=session_id,
                file_path=file_path,
                analysis_type=analysis_type,
                context=context,
                ai_result=ai_result,
                change_proposals=change_proposals,
                created_at=time.time()
            )
            
            self.analysis_sessions[session_id] = session
            
            logger.info(f"Completed analysis session {session_id} for {file_path}")
            return session
            
        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
            raise
    
    def analyze_multiple_files(self, file_paths: List[str], 
                              analysis_type: AnalysisType = AnalysisType.REVIEW) -> List[AnalysisSession]:
        """Analyze multiple files."""
        sessions = []
        
        for file_path in file_paths:
            try:
                session = self.analyze_file(file_path, analysis_type)
                sessions.append(session)
            except Exception as e:
                logger.error(f"Error analyzing {file_path}: {e}")
                continue
        
        return sessions
    
    def analyze_project(self, analysis_type: AnalysisType = AnalysisType.REVIEW,
                       file_pattern: str = "*.py") -> List[AnalysisSession]:
        """Analyze all files in the project matching a pattern."""
        try:
            # Get all files matching pattern
            all_files = self.file_manager.list_files(recursive=True)
            target_files = [
                str(f.path) for f in all_files 
                if not f.is_directory and Path(f.name).match(file_pattern)
            ]
            
            logger.info(f"Found {len(target_files)} files matching pattern {file_pattern}")
            
            return self.analyze_multiple_files(target_files, analysis_type)
            
        except Exception as e:
            logger.error(f"Error analyzing project: {e}")
            raise
    
    def _build_analysis_context(self, file_path: str, file_info: FileInfo) -> AnalysisContext:
        """Build context information for analysis."""
        try:
            # Get project files
            project_files = self.file_manager.list_files(recursive=True)
            
            # Find related files (same directory, imports, etc.)
            related_files = self._find_related_files(file_path, project_files)
            
            # Detect project type
            project_type = self._detect_project_type(project_files)
            
            # Extract dependencies
            dependencies = self._extract_dependencies(file_path, project_files)
            
            return AnalysisContext(
                project_files=project_files,
                related_files=related_files,
                project_type=project_type,
                dependencies=dependencies,
                coding_standards=None  # Could be loaded from config
            )
            
        except Exception as e:
            logger.warning(f"Error building analysis context: {e}")
            return AnalysisContext(
                project_files=[],
                related_files=[],
                project_type=None,
                dependencies=[],
                coding_standards=None
            )
    
    def _find_related_files(self, file_path: str, project_files: List[FileInfo]) -> List[str]:
        """Find files related to the target file."""
        related = []
        file_dir = Path(file_path).parent
        
        # Files in same directory
        for file_info in project_files:
            if file_info.is_directory:
                continue
                
            if file_info.path.parent == file_dir:
                related.append(str(file_info.path))
        
        # TODO: Add more sophisticated relationship detection
        # - Import analysis
        # - Class inheritance
        # - Function calls
        
        return related[:10]  # Limit to avoid overwhelming the AI
    
    def _detect_project_type(self, project_files: List[FileInfo]) -> Optional[str]:
        """Detect the type of project based on files present."""
        file_names = {f.name.lower() for f in project_files if not f.is_directory}
        
        # Python projects
        if any(name in file_names for name in ['requirements.txt', 'setup.py', 'pyproject.toml']):
            return "Python"
        
        # JavaScript/Node.js projects
        if 'package.json' in file_names:
            return "JavaScript/Node.js"
        
        # Java projects
        if any(name in file_names for name in ['pom.xml', 'build.gradle']):
            return "Java"
        
        # C# projects
        if any(f.extension == '.csproj' for f in project_files):
            return "C#"
        
        # Go projects
        if 'go.mod' in file_names:
            return "Go"
        
        # Rust projects
        if 'cargo.toml' in file_names:
            return "Rust"
        
        return None
    
    def _extract_dependencies(self, file_path: str, project_files: List[FileInfo]) -> List[str]:
        """Extract project dependencies."""
        dependencies = []
        
        try:
            # Python dependencies
            for file_info in project_files:
                if file_info.name == 'requirements.txt':
                    content = self.file_manager.read_file(str(file_info.path))
                    deps = [line.split('==')[0].split('>=')[0].split('<=')[0].strip() 
                           for line in content.split('\n') 
                           if line.strip() and not line.startswith('#')]
                    dependencies.extend(deps[:20])  # Limit to avoid overwhelming
                    break
            
            # TODO: Add support for other dependency files
            # - package.json for Node.js
            # - pom.xml for Java
            # - go.mod for Go
            # etc.
            
        except Exception as e:
            logger.warning(f"Error extracting dependencies: {e}")
        
        return dependencies
    
    def _format_context_for_ai(self, context: AnalysisContext) -> str:
        """Format analysis context for AI consumption."""
        context_parts = []
        
        if context.project_type:
            context_parts.append(f"Project Type: {context.project_type}")
        
        if context.dependencies:
            deps_str = ", ".join(context.dependencies[:10])
            context_parts.append(f"Dependencies: {deps_str}")
        
        if context.related_files:
            files_str = ", ".join(context.related_files[:5])
            context_parts.append(f"Related Files: {files_str}")
        
        return " | ".join(context_parts)
    
    def _create_proposals_from_suggestions(self, suggestions: List[CodeSuggestion], 
                                         original_content: str, file_path: str) -> List[ChangeProposal]:
        """Create change proposals from AI suggestions."""
        proposals = []
        
        for suggestion in suggestions:
            try:
                # Only create proposals for suggestions with actual code changes
                if (suggestion.suggested_code and 
                    suggestion.suggested_code.strip() != suggestion.original_code.strip()):
                    
                    proposal = self.diff_manager.create_change_proposal(
                        title=suggestion.title,
                        description=suggestion.description,
                        old_content=original_content,
                        new_content=suggestion.suggested_code,
                        file_path=file_path,
                        confidence=suggestion.confidence,
                        reasoning=suggestion.reasoning,
                        suggestion_type=suggestion.type.value
                    )
                    
                    proposals.append(proposal)
                    
            except Exception as e:
                logger.warning(f"Error creating proposal for suggestion '{suggestion.title}': {e}")
                continue
        
        return proposals
    
    def get_session(self, session_id: str) -> Optional[AnalysisSession]:
        """Get an analysis session by ID."""
        return self.analysis_sessions.get(session_id)
    
    def get_recent_sessions(self, limit: int = 10) -> List[AnalysisSession]:
        """Get recent analysis sessions."""
        sessions = list(self.analysis_sessions.values())
        sessions.sort(key=lambda s: s.created_at, reverse=True)
        return sessions[:limit]
    
    def apply_suggestion(self, session_id: str, suggestion_index: int) -> bool:
        """Apply a specific suggestion from an analysis session."""
        try:
            session = self.analysis_sessions.get(session_id)
            if not session:
                raise ValueError(f"Session {session_id} not found")
            
            if suggestion_index >= len(session.change_proposals):
                raise ValueError(f"Invalid suggestion index {suggestion_index}")
            
            proposal = session.change_proposals[suggestion_index]
            
            # Accept and apply the change
            self.diff_manager.accept_change(proposal.id)
            self.diff_manager.apply_change(proposal.id, self.file_manager)
            
            logger.info(f"Applied suggestion {suggestion_index} from session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error applying suggestion: {e}")
            raise
    
    def quick_fix_error(self, file_path: str, error_message: str) -> Optional[str]:
        """Get a quick fix for a specific error."""
        try:
            file_content = self.file_manager.read_file(file_path)
            fixed_code = self.ai_integration.quick_fix(file_content, error_message, file_path)
            
            if fixed_code and fixed_code != file_content:
                # Create a change proposal for the fix
                proposal = self.diff_manager.create_change_proposal(
                    title=f"Quick Fix: {error_message[:50]}...",
                    description=f"Automated fix for error: {error_message}",
                    old_content=file_content,
                    new_content=fixed_code,
                    file_path=file_path,
                    confidence=0.7,
                    reasoning="Automated error fix",
                    suggestion_type="bug_fix"
                )
                
                return proposal.id
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting quick fix: {e}")
            raise
    
    def get_project_summary(self) -> Dict[str, Any]:
        """Get a summary of the project and recent analysis."""
        try:
            all_files = self.file_manager.list_files(recursive=True)
            
            # File statistics
            file_stats = {
                'total_files': len([f for f in all_files if not f.is_directory]),
                'total_directories': len([f for f in all_files if f.is_directory]),
                'file_types': {}
            }
            
            for file_info in all_files:
                if not file_info.is_directory:
                    ext = file_info.extension or 'no_extension'
                    file_stats['file_types'][ext] = file_stats['file_types'].get(ext, 0) + 1
            
            # Analysis statistics
            analysis_stats = {
                'total_sessions': len(self.analysis_sessions),
                'pending_changes': len(self.diff_manager.get_pending_changes()),
                'recent_sessions': len(self.get_recent_sessions(5))
            }
            
            return {
                'project_type': self._detect_project_type(all_files),
                'file_stats': file_stats,
                'analysis_stats': analysis_stats,
                'workspace_root': str(self.file_manager.workspace_root)
            }
            
        except Exception as e:
            logger.error(f"Error getting project summary: {e}")
            return {}
