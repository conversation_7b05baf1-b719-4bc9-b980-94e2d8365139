#!/usr/bin/env python3
"""Test folder naming functionality."""

from core.analysis_engine import AnalysisEngine

def test_folder_naming():
    """Test that custom folder names work correctly."""
    print("🧪 Testing Folder Naming")
    print("=" * 40)
    
    try:
        # Initialize engine
        engine = AnalysisEngine()
        print("✅ AnalysisEngine initialized")
        
        # Test with custom folder name
        custom_folder = "WEB_APPP"
        print(f"\n🚀 Testing with custom folder: '{custom_folder}'")
        
        session_id = engine.generate_project(
            prompt="Create a simple React app",
            project_name="Test App",
            target_folder=custom_folder
        )
        
        print(f"✅ Project generated with session ID: {session_id}")
        
        # Check the session
        session = engine.get_session(session_id)
        if not session:
            print("❌ Session not found!")
            return False
        
        print(f"📋 Session found with {len(session.change_proposals)} proposals")
        
        # Check file paths
        print(f"\n📄 Generated file paths:")
        for i, proposal in enumerate(session.change_proposals):
            file_path = proposal.file_diff.file_path
            print(f"  {i+1}. {file_path}")
            
            # Check if the folder name is correct
            if '/' in file_path:
                folder_part = file_path.split('/')[0]
                if folder_part == custom_folder:
                    print(f"     ✅ Correct folder: {folder_part}")
                else:
                    print(f"     ❌ Wrong folder: {folder_part} (expected: {custom_folder})")
            else:
                print(f"     ⚠️ No folder in path: {file_path}")
        
        # Apply the project to test actual folder creation
        print(f"\n📁 Applying project to test folder creation...")
        
        success_count = 0
        for proposal in session.change_proposals:
            try:
                engine.diff_manager.accept_change(proposal.id)
                engine.diff_manager.apply_change(proposal.id, engine.file_manager)
                success_count += 1
            except Exception as e:
                print(f"❌ Failed {proposal.file_diff.file_path}: {e}")
                continue
        
        # Check if the folder was created with the correct name
        workspace_root = engine.file_manager.workspace_root
        expected_folder = workspace_root / custom_folder
        
        print(f"\n🔍 Checking folder creation:")
        print(f"   Expected folder: {expected_folder}")
        print(f"   Folder exists: {expected_folder.exists()}")
        
        if expected_folder.exists():
            files = list(expected_folder.rglob("*"))
            file_count = len([f for f in files if f.is_file()])
            print(f"   ✅ Folder '{custom_folder}' created with {file_count} files")
            
            for file_path in files:
                if file_path.is_file():
                    rel_path = file_path.relative_to(expected_folder)
                    print(f"     - {rel_path}")
            
            return True
        else:
            print(f"   ❌ Folder '{custom_folder}' was not created")
            
            # Check what folders were created instead
            all_folders = [f for f in workspace_root.iterdir() if f.is_dir()]
            print(f"   📁 Existing folders in workspace:")
            for folder in all_folders:
                print(f"     - {folder.name}")
            
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_folder_naming()
    
    if success:
        print(f"\n🎉 Folder naming test PASSED!")
        print(f"✅ Custom folder names work correctly")
        print(f"✅ Web interface should use your specified folder name")
    else:
        print(f"\n💥 Folder naming test FAILED!")
        print(f"❌ There's an issue with custom folder naming")
