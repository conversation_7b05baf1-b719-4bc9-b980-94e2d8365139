/**
 * Utility functions for the sample project
 */

// Helper function with potential improvements
function formatName(firstName, lastName) {
    // TODO: Add input validation
    return firstName + " " + lastName;
}

// Function that could be optimized
function findMax(numbers) {
    var max = numbers[0];
    for (var i = 1; i < numbers.length; i++) {
        if (numbers[i] > max) {
            max = numbers[i];
        }
    }
    return max;
}

// Function with potential bug
function calculateAverage(numbers) {
    var sum = 0;
    for (var i = 0; i < numbers.length; i++) {
        sum += numbers[i];
    }
    return sum / numbers.length; // What if numbers.length is 0?
}

// Async function that could be improved
function fetchData(url, callback) {
    var xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                callback(null, JSON.parse(xhr.responseText));
            } else {
                callback(new Error('Request failed'), null);
            }
        }
    };
    xhr.send();
}

// Export functions (if using modules)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        formatName,
        findMax,
        calculateAverage,
        fetchData
    };
}
