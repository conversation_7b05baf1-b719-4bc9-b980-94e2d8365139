#!/usr/bin/env python3
"""Test complete OpenAI integration with project generation."""

from core.analysis_engine import AnalysisEngine

def test_complete_openai_integration():
    """Test complete OpenAI integration with project generation."""
    print("🧪 Testing complete OpenAI integration...")
    
    try:
        # Initialize engine (should use OpenAI by default)
        engine = AnalysisEngine()
        print("✅ AnalysisEngine initialized with OpenAI")
        
        # Test project generation with OpenAI
        print("\n🚀 Testing project generation with OpenAI...")
        session_id = engine.generate_project(
            prompt="Create a modern React todo app with TypeScript, state management, and local storage",
            project_name="OpenAI React Todo",
            preferred_framework="React",
            preferred_language="TypeScript",
            target_folder="openai_react_todo"
        )
        
        print(f"✅ Project generated with session ID: {session_id}")
        
        # Check the session
        session = engine.get_session(session_id)
        if session and session.change_proposals:
            print(f"📁 Generated {len(session.change_proposals)} files:")
            for proposal in session.change_proposals:
                file_path = proposal.file_diff.file_path
                print(f"  - {file_path}")
        
        # Apply the project
        print("\n📁 Applying OpenAI-generated project...")
        success = engine.apply_project_generation(session_id)
        print(f"✅ Apply result: {success}")
        
        # Verify the project was created
        import os
        workspace_path = engine.file_manager.workspace_root
        project_path = workspace_path / "openai_react_todo"
        
        if project_path.exists():
            print(f"✅ OpenAI project folder created: {project_path}")
            
            # List all files
            all_files = []
            for root, dirs, files in os.walk(project_path):
                for file in files:
                    rel_path = os.path.relpath(os.path.join(root, file), project_path)
                    all_files.append(rel_path)
            
            print(f"📄 OpenAI generated files ({len(all_files)} total):")
            for file_path in sorted(all_files):
                print(f"  - {file_path}")
            
            # Check for TypeScript files
            ts_files = [f for f in all_files if f.endswith('.ts') or f.endswith('.tsx')]
            if ts_files:
                print(f"✅ TypeScript files found: {len(ts_files)}")
                for ts_file in ts_files[:3]:  # Show first 3
                    print(f"  - {ts_file}")
            
            # Check a sample file content
            if all_files:
                sample_file = project_path / all_files[0]
                if sample_file.exists() and sample_file.is_file():
                    with open(sample_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    print(f"\n📄 Sample file content ({all_files[0]}):")
                    print(f"```\n{content[:300]}...\n```")
        else:
            print(f"❌ OpenAI project folder not found: {project_path}")
        
        # Test code analysis with OpenAI
        print("\n🔍 Testing code analysis with OpenAI...")

        # Create a test file to analyze
        test_file_path = project_path / "test_analysis.js"
        test_code = '''
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n-1) + fibonacci(n-2);
}

console.log(fibonacci(10));
'''

        with open(test_file_path, 'w') as f:
            f.write(test_code)

        from core.ai_integration import AnalysisType
        analysis_result = engine.analyze_file(
            file_path=str(test_file_path.relative_to(workspace_path)),
            analysis_type=AnalysisType.OPTIMIZE
        )
        
        print(f"✅ Code analysis completed!")
        print(f"📝 Summary: {analysis_result.ai_result.summary[:100]}...")
        print(f"💡 Suggestions: {len(analysis_result.ai_result.suggestions)}")
        
        for i, suggestion in enumerate(analysis_result.ai_result.suggestions[:2]):
            print(f"  {i+1}. {suggestion.title}: {suggestion.description[:80]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_complete_openai_integration()
    if success:
        print("\n🎉 Complete OpenAI integration test passed!")
        print("✅ OpenAI ChatGPT project generation working")
        print("✅ OpenAI code analysis working")
        print("✅ File creation and folder management working")
        print("🌐 Web interface now uses OpenAI for superior code generation!")
    else:
        print("\n💥 Complete OpenAI integration test failed!")
