#!/usr/bin/env python3
"""Test script to verify web interface framework fix."""

from core.analysis_engine import AnalysisEngine

def test_web_interface_frameworks():
    """Test the exact same calls that the web interface makes."""
    print("🧪 Testing web interface framework calls...")
    
    try:
        engine = AnalysisEngine()
        
        # Simulate web interface call with React
        print("\n🧪 Test: Web interface React call")
        session_id = engine.generate_project(
            prompt="Create a simple React todo app",
            project_name="React Todo",
            preferred_framework="React",  # Exactly as web interface sends
            preferred_language="JavaScript",
            include_tests=True,
            include_docs=True,
            target_folder="react_web_test"
        )
        
        print(f"✅ React project generated: {session_id}")
        
        # Check the session
        session = engine.get_session(session_id)
        if session and session.change_proposals:
            print(f"📁 Generated {len(session.change_proposals)} files:")
            for proposal in session.change_proposals[:3]:  # Show first 3
                print(f"  - {proposal.file_diff.file_path}")
        
        # Apply the project to test full workflow
        print("\n📁 Applying project...")
        success = engine.apply_project_generation(session_id)
        print(f"✅ Apply result: {success}")
        
        # Verify folder was created
        import os
        workspace_path = engine.file_manager.workspace_root
        project_path = workspace_path / "react_web_test"
        
        if project_path.exists():
            print(f"✅ Project folder created: {project_path}")
            files = list(project_path.glob("*"))
            print(f"📄 Files in project:")
            for file_path in files:
                if file_path.is_file():
                    print(f"  - {file_path.name}")
        else:
            print(f"❌ Project folder not found: {project_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_web_interface_frameworks()
    if success:
        print("\n🎉 Web interface framework fix is working!")
        print("🌐 The web interface should now work without 'React' is not a valid Framework errors!")
    else:
        print("\n💥 Web interface framework fix test failed!")
