"""Diff generation and change management system."""

import difflib
import logging
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import json
import time

logger = logging.getLogger(__name__)

class ChangeStatus(Enum):
    """Status of a proposed change."""
    PENDING = "pending"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    APPLIED = "applied"

@dataclass
class DiffLine:
    """A single line in a diff."""
    line_type: str  # '+', '-', ' ', '?'
    content: str
    line_number_old: Optional[int]
    line_number_new: Optional[int]

@dataclass
class DiffHunk:
    """A hunk (section) of a diff."""
    old_start: int
    old_count: int
    new_start: int
    new_count: int
    lines: List[DiffLine]
    context: str

@dataclass
class FileDiff:
    """Diff for a single file."""
    file_path: str
    old_content: str
    new_content: str
    hunks: List[DiffHunk]
    stats: Dict[str, int]  # additions, deletions, changes

@dataclass
class ChangeProposal:
    """A proposed change with metadata."""
    id: str
    title: str
    description: str
    file_diff: FileDiff
    status: ChangeStatus
    created_at: float
    confidence: float
    reasoning: str
    suggestion_type: str
    is_new_file: bool = False
    project_id: Optional[str] = None

@dataclass
class ProjectChangeProposal:
    """A collection of related changes for a project."""
    id: str
    name: str
    description: str
    change_proposals: List[ChangeProposal]
    status: ChangeStatus
    created_at: float
    project_type: str
    estimated_complexity: str

class DiffManager:
    """Manages diff generation and change proposals."""
    
    def __init__(self):
        """Initialize diff manager."""
        self.pending_changes: Dict[str, ChangeProposal] = {}
        self.change_history: List[ChangeProposal] = []
        self.project_changes: Dict[str, ProjectChangeProposal] = {}
        self.project_history: List[ProjectChangeProposal] = []
        logger.info("DiffManager initialized")
    
    def create_diff(self, old_content: str, new_content: str, 
                   file_path: str, context_lines: int = 3) -> FileDiff:
        """Create a detailed diff between old and new content."""
        try:
            old_lines = old_content.splitlines(keepends=True)
            new_lines = new_content.splitlines(keepends=True)
            
            # Generate unified diff
            diff_lines = list(difflib.unified_diff(
                old_lines, new_lines,
                fromfile=f"a/{file_path}",
                tofile=f"b/{file_path}",
                n=context_lines
            ))
            
            # Parse diff into hunks
            hunks = self._parse_unified_diff(diff_lines, old_lines, new_lines)
            
            # Calculate statistics
            stats = self._calculate_diff_stats(hunks)
            
            return FileDiff(
                file_path=file_path,
                old_content=old_content,
                new_content=new_content,
                hunks=hunks,
                stats=stats
            )
            
        except Exception as e:
            logger.error(f"Error creating diff for {file_path}: {e}")
            raise
    
    def _parse_unified_diff(self, diff_lines: List[str], 
                           old_lines: List[str], new_lines: List[str]) -> List[DiffHunk]:
        """Parse unified diff output into structured hunks."""
        hunks = []
        current_hunk = None
        
        for line in diff_lines:
            if line.startswith('@@'):
                # New hunk header
                if current_hunk:
                    hunks.append(current_hunk)
                
                # Parse hunk header: @@ -old_start,old_count +new_start,new_count @@
                header_parts = line.split()
                old_info = header_parts[1][1:]  # Remove '-'
                new_info = header_parts[2][1:]  # Remove '+'
                
                old_start, old_count = map(int, old_info.split(',')) if ',' in old_info else (int(old_info), 1)
                new_start, new_count = map(int, new_info.split(',')) if ',' in new_info else (int(new_info), 1)
                
                current_hunk = DiffHunk(
                    old_start=old_start,
                    old_count=old_count,
                    new_start=new_start,
                    new_count=new_count,
                    lines=[],
                    context=line
                )
                
            elif current_hunk and (line.startswith(' ') or line.startswith('+') or line.startswith('-')):
                # Diff line
                line_type = line[0]
                content = line[1:]
                
                # Calculate line numbers
                old_line_num = None
                new_line_num = None
                
                if line_type == ' ':  # Context line
                    old_line_num = current_hunk.old_start + len([l for l in current_hunk.lines if l.line_type in [' ', '-']])
                    new_line_num = current_hunk.new_start + len([l for l in current_hunk.lines if l.line_type in [' ', '+']])
                elif line_type == '-':  # Deletion
                    old_line_num = current_hunk.old_start + len([l for l in current_hunk.lines if l.line_type in [' ', '-']])
                elif line_type == '+':  # Addition
                    new_line_num = current_hunk.new_start + len([l for l in current_hunk.lines if l.line_type in [' ', '+']])
                
                diff_line = DiffLine(
                    line_type=line_type,
                    content=content,
                    line_number_old=old_line_num,
                    line_number_new=new_line_num
                )
                
                current_hunk.lines.append(diff_line)
        
        if current_hunk:
            hunks.append(current_hunk)
        
        return hunks
    
    def _calculate_diff_stats(self, hunks: List[DiffHunk]) -> Dict[str, int]:
        """Calculate diff statistics."""
        additions = 0
        deletions = 0
        
        for hunk in hunks:
            for line in hunk.lines:
                if line.line_type == '+':
                    additions += 1
                elif line.line_type == '-':
                    deletions += 1
        
        return {
            'additions': additions,
            'deletions': deletions,
            'changes': additions + deletions
        }
    
    def create_change_proposal(self, title: str, description: str, 
                              old_content: str, new_content: str, 
                              file_path: str, confidence: float = 0.5,
                              reasoning: str = "", suggestion_type: str = "improvement") -> ChangeProposal:
        """Create a new change proposal."""
        try:
            # Generate unique ID
            change_id = f"change_{int(time.time() * 1000)}"
            
            # Create diff
            file_diff = self.create_diff(old_content, new_content, file_path)
            
            # Create proposal
            proposal = ChangeProposal(
                id=change_id,
                title=title,
                description=description,
                file_diff=file_diff,
                status=ChangeStatus.PENDING,
                created_at=time.time(),
                confidence=confidence,
                reasoning=reasoning,
                suggestion_type=suggestion_type
            )
            
            # Store proposal
            self.pending_changes[change_id] = proposal
            
            logger.info(f"Created change proposal {change_id} for {file_path}")
            return proposal
            
        except Exception as e:
            logger.error(f"Error creating change proposal: {e}")
            raise
    
    def get_pending_changes(self) -> List[ChangeProposal]:
        """Get all pending change proposals."""
        return list(self.pending_changes.values())
    
    def get_change_proposal(self, change_id: str) -> Optional[ChangeProposal]:
        """Get a specific change proposal."""
        return self.pending_changes.get(change_id)
    
    def accept_change(self, change_id: str) -> bool:
        """Accept a change proposal."""
        try:
            proposal = self.pending_changes.get(change_id)
            if not proposal:
                raise ValueError(f"Change proposal {change_id} not found")
            
            if proposal.status != ChangeStatus.PENDING:
                raise ValueError(f"Change proposal {change_id} is not pending")
            
            proposal.status = ChangeStatus.ACCEPTED
            logger.info(f"Accepted change proposal {change_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error accepting change {change_id}: {e}")
            raise
    
    def reject_change(self, change_id: str, reason: str = "") -> bool:
        """Reject a change proposal."""
        try:
            proposal = self.pending_changes.get(change_id)
            if not proposal:
                raise ValueError(f"Change proposal {change_id} not found")
            
            if proposal.status != ChangeStatus.PENDING:
                raise ValueError(f"Change proposal {change_id} is not pending")
            
            proposal.status = ChangeStatus.REJECTED
            if reason:
                proposal.description += f"\n\nRejection reason: {reason}"
            
            # Move to history
            self.change_history.append(proposal)
            del self.pending_changes[change_id]
            
            logger.info(f"Rejected change proposal {change_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error rejecting change {change_id}: {e}")
            raise
    
    def apply_change(self, change_id: str, file_manager) -> bool:
        """Apply an accepted change to the file system."""
        try:
            proposal = self.pending_changes.get(change_id)
            if not proposal:
                raise ValueError(f"Change proposal {change_id} not found")
            
            if proposal.status != ChangeStatus.ACCEPTED:
                raise ValueError(f"Change proposal {change_id} is not accepted")
            
            # Apply the change
            file_manager.write_file(
                proposal.file_diff.file_path,
                proposal.file_diff.new_content
            )
            
            proposal.status = ChangeStatus.APPLIED
            
            # Move to history
            self.change_history.append(proposal)
            del self.pending_changes[change_id]
            
            logger.info(f"Applied change proposal {change_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error applying change {change_id}: {e}")
            raise
    
    def get_change_history(self, limit: int = 50) -> List[ChangeProposal]:
        """Get change history."""
        return self.change_history[-limit:]
    
    def format_diff_for_display(self, file_diff: FileDiff, 
                               max_context_lines: int = 5) -> str:
        """Format diff for human-readable display."""
        try:
            output = []
            output.append(f"File: {file_diff.file_path}")
            output.append(f"Changes: +{file_diff.stats['additions']} -{file_diff.stats['deletions']}")
            output.append("=" * 50)
            
            for hunk in file_diff.hunks:
                output.append(f"\n{hunk.context}")
                
                for line in hunk.lines:
                    prefix = ""
                    if line.line_type == '+':
                        prefix = f"+{line.line_number_new or '?':4d}: "
                    elif line.line_type == '-':
                        prefix = f"-{line.line_number_old or '?':4d}: "
                    else:
                        prefix = f" {line.line_number_old or '?':4d}: "
                    
                    output.append(f"{prefix}{line.content.rstrip()}")
            
            return "\n".join(output)
            
        except Exception as e:
            logger.error(f"Error formatting diff: {e}")
            return f"Error formatting diff: {e}"
    
    def export_changes(self, file_path: str) -> bool:
        """Export pending changes to a file."""
        try:
            export_data = {
                'pending_changes': [
                    {
                        'id': proposal.id,
                        'title': proposal.title,
                        'description': proposal.description,
                        'file_path': proposal.file_diff.file_path,
                        'status': proposal.status.value,
                        'created_at': proposal.created_at,
                        'confidence': proposal.confidence,
                        'reasoning': proposal.reasoning,
                        'suggestion_type': proposal.suggestion_type,
                        'stats': proposal.file_diff.stats
                    }
                    for proposal in self.pending_changes.values()
                ],
                'exported_at': time.time()
            }
            
            with open(file_path, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            logger.info(f"Exported changes to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting changes: {e}")
            raise
    
    def clear_pending_changes(self) -> int:
        """Clear all pending changes."""
        count = len(self.pending_changes)
        self.pending_changes.clear()
        logger.info(f"Cleared {count} pending changes")
        return count

    def create_project_change_proposal(self, name: str, description: str,
                                     change_proposals: List[ChangeProposal],
                                     project_type: str = "generated",
                                     estimated_complexity: str = "medium") -> ProjectChangeProposal:
        """Create a project-level change proposal."""
        try:
            # Generate unique ID
            project_id = f"project_{int(time.time() * 1000)}"

            # Update individual proposals with project ID
            for proposal in change_proposals:
                proposal.project_id = project_id

            # Create project proposal
            project_proposal = ProjectChangeProposal(
                id=project_id,
                name=name,
                description=description,
                change_proposals=change_proposals,
                status=ChangeStatus.PENDING,
                created_at=time.time(),
                project_type=project_type,
                estimated_complexity=estimated_complexity
            )

            # Store project proposal
            self.project_changes[project_id] = project_proposal

            logger.info(f"Created project change proposal {project_id} with {len(change_proposals)} files")
            return project_proposal

        except Exception as e:
            logger.error(f"Error creating project change proposal: {e}")
            raise

    def get_project_change_proposal(self, project_id: str) -> Optional[ProjectChangeProposal]:
        """Get a specific project change proposal."""
        return self.project_changes.get(project_id)

    def get_pending_project_changes(self) -> List[ProjectChangeProposal]:
        """Get all pending project change proposals."""
        return [p for p in self.project_changes.values() if p.status == ChangeStatus.PENDING]

    def accept_project_change(self, project_id: str) -> bool:
        """Accept a project change proposal."""
        try:
            project_proposal = self.project_changes.get(project_id)
            if not project_proposal:
                raise ValueError(f"Project change proposal {project_id} not found")

            if project_proposal.status != ChangeStatus.PENDING:
                raise ValueError(f"Project change proposal {project_id} is not pending")

            # Accept all individual changes
            for change_proposal in project_proposal.change_proposals:
                if change_proposal.status == ChangeStatus.PENDING:
                    change_proposal.status = ChangeStatus.ACCEPTED

            project_proposal.status = ChangeStatus.ACCEPTED
            logger.info(f"Accepted project change proposal {project_id}")
            return True

        except Exception as e:
            logger.error(f"Error accepting project change {project_id}: {e}")
            raise

    def reject_project_change(self, project_id: str, reason: str = "") -> bool:
        """Reject a project change proposal."""
        try:
            project_proposal = self.project_changes.get(project_id)
            if not project_proposal:
                raise ValueError(f"Project change proposal {project_id} not found")

            if project_proposal.status != ChangeStatus.PENDING:
                raise ValueError(f"Project change proposal {project_id} is not pending")

            # Reject all individual changes
            for change_proposal in project_proposal.change_proposals:
                if change_proposal.status == ChangeStatus.PENDING:
                    change_proposal.status = ChangeStatus.REJECTED
                    if reason:
                        change_proposal.description += f"\n\nRejection reason: {reason}"

            project_proposal.status = ChangeStatus.REJECTED
            if reason:
                project_proposal.description += f"\n\nRejection reason: {reason}"

            # Move to history
            self.project_history.append(project_proposal)
            del self.project_changes[project_id]

            logger.info(f"Rejected project change proposal {project_id}")
            return True

        except Exception as e:
            logger.error(f"Error rejecting project change {project_id}: {e}")
            raise

    def apply_project_change(self, project_id: str, file_manager,
                           selected_files: Optional[List[str]] = None) -> bool:
        """Apply an accepted project change proposal."""
        try:
            project_proposal = self.project_changes.get(project_id)
            if not project_proposal:
                raise ValueError(f"Project change proposal {project_id} not found")

            if project_proposal.status != ChangeStatus.ACCEPTED:
                raise ValueError(f"Project change proposal {project_id} is not accepted")

            # Determine which files to apply
            if selected_files is None:
                changes_to_apply = project_proposal.change_proposals
            else:
                changes_to_apply = [
                    c for c in project_proposal.change_proposals
                    if c.file_diff.file_path in selected_files
                ]

            # Apply changes
            applied_count = 0
            for change_proposal in changes_to_apply:
                try:
                    if change_proposal.status == ChangeStatus.ACCEPTED:
                        # Create directory if needed for new files
                        if change_proposal.is_new_file:
                            file_path = Path(change_proposal.file_diff.file_path)
                            if file_path.parent != Path('.'):
                                file_manager.create_directory(str(file_path.parent))

                        # Apply the change
                        file_manager.write_file(
                            change_proposal.file_diff.file_path,
                            change_proposal.file_diff.new_content
                        )

                        change_proposal.status = ChangeStatus.APPLIED
                        applied_count += 1

                except Exception as e:
                    logger.error(f"Error applying change for {change_proposal.file_diff.file_path}: {e}")
                    continue

            # Update project status if all changes applied
            if applied_count == len(changes_to_apply):
                project_proposal.status = ChangeStatus.APPLIED

            # Move to history
            self.project_history.append(project_proposal)
            del self.project_changes[project_id]

            logger.info(f"Applied project change proposal {project_id}: {applied_count}/{len(changes_to_apply)} files")
            return applied_count > 0

        except Exception as e:
            logger.error(f"Error applying project change {project_id}: {e}")
            raise

    def get_project_change_history(self, limit: int = 20) -> List[ProjectChangeProposal]:
        """Get project change history."""
        return self.project_history[-limit:]

    def get_project_structure_preview(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Get a preview of the project structure."""
        try:
            project_proposal = self.project_changes.get(project_id)
            if not project_proposal:
                return None

            # Group files by directory
            files_by_directory = {}
            total_size = 0

            for change in project_proposal.change_proposals:
                file_path = change.file_diff.file_path
                directory = str(Path(file_path).parent) if Path(file_path).parent != Path('.') else "root"

                if directory not in files_by_directory:
                    files_by_directory[directory] = []

                file_size = len(change.file_diff.new_content)
                total_size += file_size

                files_by_directory[directory].append({
                    'name': Path(file_path).name,
                    'path': file_path,
                    'size': file_size,
                    'description': change.description,
                    'is_new_file': change.is_new_file,
                    'change_id': change.id
                })

            return {
                'project_id': project_id,
                'name': project_proposal.name,
                'description': project_proposal.description,
                'project_type': project_proposal.project_type,
                'complexity': project_proposal.estimated_complexity,
                'total_files': len(project_proposal.change_proposals),
                'total_size': total_size,
                'directories': files_by_directory,
                'status': project_proposal.status.value,
                'created_at': project_proposal.created_at
            }

        except Exception as e:
            logger.error(f"Error getting project structure preview: {e}")
            return None
