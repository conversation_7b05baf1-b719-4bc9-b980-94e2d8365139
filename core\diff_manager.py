"""Diff generation and change management system."""

import difflib
import logging
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import json
import time

logger = logging.getLogger(__name__)

class ChangeStatus(Enum):
    """Status of a proposed change."""
    PENDING = "pending"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    APPLIED = "applied"

@dataclass
class DiffLine:
    """A single line in a diff."""
    line_type: str  # '+', '-', ' ', '?'
    content: str
    line_number_old: Optional[int]
    line_number_new: Optional[int]

@dataclass
class DiffHunk:
    """A hunk (section) of a diff."""
    old_start: int
    old_count: int
    new_start: int
    new_count: int
    lines: List[DiffLine]
    context: str

@dataclass
class FileDiff:
    """Diff for a single file."""
    file_path: str
    old_content: str
    new_content: str
    hunks: List[DiffHunk]
    stats: Dict[str, int]  # additions, deletions, changes

@dataclass
class ChangeProposal:
    """A proposed change with metadata."""
    id: str
    title: str
    description: str
    file_diff: FileDiff
    status: ChangeStatus
    created_at: float
    confidence: float
    reasoning: str
    suggestion_type: str

class DiffManager:
    """Manages diff generation and change proposals."""
    
    def __init__(self):
        """Initialize diff manager."""
        self.pending_changes: Dict[str, ChangeProposal] = {}
        self.change_history: List[ChangeProposal] = []
        logger.info("DiffManager initialized")
    
    def create_diff(self, old_content: str, new_content: str, 
                   file_path: str, context_lines: int = 3) -> FileDiff:
        """Create a detailed diff between old and new content."""
        try:
            old_lines = old_content.splitlines(keepends=True)
            new_lines = new_content.splitlines(keepends=True)
            
            # Generate unified diff
            diff_lines = list(difflib.unified_diff(
                old_lines, new_lines,
                fromfile=f"a/{file_path}",
                tofile=f"b/{file_path}",
                n=context_lines
            ))
            
            # Parse diff into hunks
            hunks = self._parse_unified_diff(diff_lines, old_lines, new_lines)
            
            # Calculate statistics
            stats = self._calculate_diff_stats(hunks)
            
            return FileDiff(
                file_path=file_path,
                old_content=old_content,
                new_content=new_content,
                hunks=hunks,
                stats=stats
            )
            
        except Exception as e:
            logger.error(f"Error creating diff for {file_path}: {e}")
            raise
    
    def _parse_unified_diff(self, diff_lines: List[str], 
                           old_lines: List[str], new_lines: List[str]) -> List[DiffHunk]:
        """Parse unified diff output into structured hunks."""
        hunks = []
        current_hunk = None
        
        for line in diff_lines:
            if line.startswith('@@'):
                # New hunk header
                if current_hunk:
                    hunks.append(current_hunk)
                
                # Parse hunk header: @@ -old_start,old_count +new_start,new_count @@
                header_parts = line.split()
                old_info = header_parts[1][1:]  # Remove '-'
                new_info = header_parts[2][1:]  # Remove '+'
                
                old_start, old_count = map(int, old_info.split(',')) if ',' in old_info else (int(old_info), 1)
                new_start, new_count = map(int, new_info.split(',')) if ',' in new_info else (int(new_info), 1)
                
                current_hunk = DiffHunk(
                    old_start=old_start,
                    old_count=old_count,
                    new_start=new_start,
                    new_count=new_count,
                    lines=[],
                    context=line
                )
                
            elif current_hunk and (line.startswith(' ') or line.startswith('+') or line.startswith('-')):
                # Diff line
                line_type = line[0]
                content = line[1:]
                
                # Calculate line numbers
                old_line_num = None
                new_line_num = None
                
                if line_type == ' ':  # Context line
                    old_line_num = current_hunk.old_start + len([l for l in current_hunk.lines if l.line_type in [' ', '-']])
                    new_line_num = current_hunk.new_start + len([l for l in current_hunk.lines if l.line_type in [' ', '+']])
                elif line_type == '-':  # Deletion
                    old_line_num = current_hunk.old_start + len([l for l in current_hunk.lines if l.line_type in [' ', '-']])
                elif line_type == '+':  # Addition
                    new_line_num = current_hunk.new_start + len([l for l in current_hunk.lines if l.line_type in [' ', '+']])
                
                diff_line = DiffLine(
                    line_type=line_type,
                    content=content,
                    line_number_old=old_line_num,
                    line_number_new=new_line_num
                )
                
                current_hunk.lines.append(diff_line)
        
        if current_hunk:
            hunks.append(current_hunk)
        
        return hunks
    
    def _calculate_diff_stats(self, hunks: List[DiffHunk]) -> Dict[str, int]:
        """Calculate diff statistics."""
        additions = 0
        deletions = 0
        
        for hunk in hunks:
            for line in hunk.lines:
                if line.line_type == '+':
                    additions += 1
                elif line.line_type == '-':
                    deletions += 1
        
        return {
            'additions': additions,
            'deletions': deletions,
            'changes': additions + deletions
        }
    
    def create_change_proposal(self, title: str, description: str, 
                              old_content: str, new_content: str, 
                              file_path: str, confidence: float = 0.5,
                              reasoning: str = "", suggestion_type: str = "improvement") -> ChangeProposal:
        """Create a new change proposal."""
        try:
            # Generate unique ID
            change_id = f"change_{int(time.time() * 1000)}"
            
            # Create diff
            file_diff = self.create_diff(old_content, new_content, file_path)
            
            # Create proposal
            proposal = ChangeProposal(
                id=change_id,
                title=title,
                description=description,
                file_diff=file_diff,
                status=ChangeStatus.PENDING,
                created_at=time.time(),
                confidence=confidence,
                reasoning=reasoning,
                suggestion_type=suggestion_type
            )
            
            # Store proposal
            self.pending_changes[change_id] = proposal
            
            logger.info(f"Created change proposal {change_id} for {file_path}")
            return proposal
            
        except Exception as e:
            logger.error(f"Error creating change proposal: {e}")
            raise
    
    def get_pending_changes(self) -> List[ChangeProposal]:
        """Get all pending change proposals."""
        return list(self.pending_changes.values())
    
    def get_change_proposal(self, change_id: str) -> Optional[ChangeProposal]:
        """Get a specific change proposal."""
        return self.pending_changes.get(change_id)
    
    def accept_change(self, change_id: str) -> bool:
        """Accept a change proposal."""
        try:
            proposal = self.pending_changes.get(change_id)
            if not proposal:
                raise ValueError(f"Change proposal {change_id} not found")
            
            if proposal.status != ChangeStatus.PENDING:
                raise ValueError(f"Change proposal {change_id} is not pending")
            
            proposal.status = ChangeStatus.ACCEPTED
            logger.info(f"Accepted change proposal {change_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error accepting change {change_id}: {e}")
            raise
    
    def reject_change(self, change_id: str, reason: str = "") -> bool:
        """Reject a change proposal."""
        try:
            proposal = self.pending_changes.get(change_id)
            if not proposal:
                raise ValueError(f"Change proposal {change_id} not found")
            
            if proposal.status != ChangeStatus.PENDING:
                raise ValueError(f"Change proposal {change_id} is not pending")
            
            proposal.status = ChangeStatus.REJECTED
            if reason:
                proposal.description += f"\n\nRejection reason: {reason}"
            
            # Move to history
            self.change_history.append(proposal)
            del self.pending_changes[change_id]
            
            logger.info(f"Rejected change proposal {change_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error rejecting change {change_id}: {e}")
            raise
    
    def apply_change(self, change_id: str, file_manager) -> bool:
        """Apply an accepted change to the file system."""
        try:
            proposal = self.pending_changes.get(change_id)
            if not proposal:
                raise ValueError(f"Change proposal {change_id} not found")
            
            if proposal.status != ChangeStatus.ACCEPTED:
                raise ValueError(f"Change proposal {change_id} is not accepted")
            
            # Apply the change
            file_manager.write_file(
                proposal.file_diff.file_path,
                proposal.file_diff.new_content
            )
            
            proposal.status = ChangeStatus.APPLIED
            
            # Move to history
            self.change_history.append(proposal)
            del self.pending_changes[change_id]
            
            logger.info(f"Applied change proposal {change_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error applying change {change_id}: {e}")
            raise
    
    def get_change_history(self, limit: int = 50) -> List[ChangeProposal]:
        """Get change history."""
        return self.change_history[-limit:]
    
    def format_diff_for_display(self, file_diff: FileDiff, 
                               max_context_lines: int = 5) -> str:
        """Format diff for human-readable display."""
        try:
            output = []
            output.append(f"File: {file_diff.file_path}")
            output.append(f"Changes: +{file_diff.stats['additions']} -{file_diff.stats['deletions']}")
            output.append("=" * 50)
            
            for hunk in file_diff.hunks:
                output.append(f"\n{hunk.context}")
                
                for line in hunk.lines:
                    prefix = ""
                    if line.line_type == '+':
                        prefix = f"+{line.line_number_new or '?':4d}: "
                    elif line.line_type == '-':
                        prefix = f"-{line.line_number_old or '?':4d}: "
                    else:
                        prefix = f" {line.line_number_old or '?':4d}: "
                    
                    output.append(f"{prefix}{line.content.rstrip()}")
            
            return "\n".join(output)
            
        except Exception as e:
            logger.error(f"Error formatting diff: {e}")
            return f"Error formatting diff: {e}"
    
    def export_changes(self, file_path: str) -> bool:
        """Export pending changes to a file."""
        try:
            export_data = {
                'pending_changes': [
                    {
                        'id': proposal.id,
                        'title': proposal.title,
                        'description': proposal.description,
                        'file_path': proposal.file_diff.file_path,
                        'status': proposal.status.value,
                        'created_at': proposal.created_at,
                        'confidence': proposal.confidence,
                        'reasoning': proposal.reasoning,
                        'suggestion_type': proposal.suggestion_type,
                        'stats': proposal.file_diff.stats
                    }
                    for proposal in self.pending_changes.values()
                ],
                'exported_at': time.time()
            }
            
            with open(file_path, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            logger.info(f"Exported changes to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting changes: {e}")
            raise
    
    def clear_pending_changes(self) -> int:
        """Clear all pending changes."""
        count = len(self.pending_changes)
        self.pending_changes.clear()
        logger.info(f"Cleared {count} pending changes")
        return count
