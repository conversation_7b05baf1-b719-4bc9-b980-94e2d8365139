"""
cli_tool

A basic command-line interface tool built using Python.
"""

import click
from rich.console import Console
from rich.table import Table

console = Console()

@click.group()
@click.version_option(version="1.0.0")
def cli():
    """
    cli_tool

    A basic command-line interface tool built using Python.
    """
    pass

@cli.command()
@click.option('--name', default='World', help='Name to greet')
def hello(name):
    """Say hello to someone."""
    console.print(f"Hello, {name}!", style="bold green")

@cli.command()
def features():
    """Show available features."""
    table = Table(title="Available Features")
    table.add_column("Feature", style="cyan")
    table.add_column("Description", style="white")

    features = []
    for i, feature in enumerate(features, 1):
        table.add_row(f"Feature {i}", feature)

    console.print(table)

@cli.command()
def info():
    """Show project information."""
    console.print(f"[bold]cli_tool[/bold]")
    console.print(f"A basic command-line interface tool built using Python.")
    console.print(f"Version: 1.0.0")

if __name__ == '__main__':
    cli()
