"""Security and permission management for the AI Code Assistant."""

import os
import stat
import logging
from pathlib import Path
from typing import List, Set, Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum
import hashlib
import time

from config import Config

logger = logging.getLogger(__name__)

class PermissionLevel(Enum):
    """Permission levels for file operations."""
    READ_ONLY = "read_only"
    READ_WRITE = "read_write"
    FULL_ACCESS = "full_access"
    RESTRICTED = "restricted"

class SecurityViolationType(Enum):
    """Types of security violations."""
    PATH_TRAVERSAL = "path_traversal"
    FORBIDDEN_PATH = "forbidden_path"
    UNAUTHORIZED_EXTENSION = "unauthorized_extension"
    FILE_SIZE_EXCEEDED = "file_size_exceeded"
    PERMISSION_DENIED = "permission_denied"
    SUSPICIOUS_CONTENT = "suspicious_content"

@dataclass
class SecurityViolation:
    """A security violation record."""
    violation_type: SecurityViolationType
    path: str
    description: str
    timestamp: float
    severity: str  # "low", "medium", "high", "critical"

@dataclass
class FilePermission:
    """File permission configuration."""
    path: Path
    permission_level: PermissionLevel
    allowed_operations: Set[str]
    restrictions: Dict[str, Any]

class SecurityManager:
    """Manages security and permissions for file operations."""
    
    def __init__(self, workspace_root: Optional[Path] = None):
        """Initialize security manager."""
        self.workspace_root = workspace_root or Config.WORKSPACE_ROOT
        self.workspace_root = self.workspace_root.resolve()
        
        # Security state
        self.violations: List[SecurityViolation] = []
        self.file_permissions: Dict[str, FilePermission] = {}
        self.access_log: List[Dict[str, Any]] = []
        
        # Security patterns
        self.suspicious_patterns = {
            'system_commands': [
                r'os\.system\(',
                r'subprocess\.',
                r'exec\(',
                r'eval\(',
                r'__import__\(',
                r'open\(.+["\']w["\']',
                r'rm\s+-rf',
                r'del\s+/[sq]',
                r'format\s+c:',
            ],
            'network_access': [
                r'urllib\.',
                r'requests\.',
                r'socket\.',
                r'http\.',
                r'ftp\.',
            ],
            'file_operations': [
                r'shutil\.',
                r'os\.remove',
                r'os\.unlink',
                r'pathlib\.Path\(.+\)\.unlink',
            ]
        }
        
        logger.info(f"SecurityManager initialized for workspace: {self.workspace_root}")
    
    def validate_path(self, path: Path, operation: str = "read") -> bool:
        """Validate a path for security compliance."""
        try:
            # Convert to absolute path and resolve
            abs_path = (self.workspace_root / path).resolve()
            
            # Check if path is within workspace
            try:
                abs_path.relative_to(self.workspace_root)
            except ValueError:
                self._log_violation(
                    SecurityViolationType.PATH_TRAVERSAL,
                    str(path),
                    f"Path outside workspace: {path}",
                    "high"
                )
                return False
            
            # Check against forbidden paths
            if not self._is_path_allowed(abs_path):
                self._log_violation(
                    SecurityViolationType.FORBIDDEN_PATH,
                    str(path),
                    f"Access to forbidden path: {path}",
                    "critical"
                )
                return False
            
            # Check file extension restrictions
            if abs_path.is_file() and not self._is_extension_allowed(abs_path):
                self._log_violation(
                    SecurityViolationType.UNAUTHORIZED_EXTENSION,
                    str(path),
                    f"Unauthorized file extension: {abs_path.suffix}",
                    "medium"
                )
                return False
            
            # Check file size for read operations
            if operation == "read" and abs_path.exists() and abs_path.is_file():
                if abs_path.stat().st_size > Config.MAX_FILE_SIZE_BYTES:
                    self._log_violation(
                        SecurityViolationType.FILE_SIZE_EXCEEDED,
                        str(path),
                        f"File size exceeds limit: {abs_path.stat().st_size} bytes",
                        "medium"
                    )
                    return False
            
            # Log access
            self._log_access(str(path), operation, True)
            return True
            
        except Exception as e:
            logger.error(f"Error validating path {path}: {e}")
            self._log_violation(
                SecurityViolationType.PERMISSION_DENIED,
                str(path),
                f"Path validation error: {e}",
                "high"
            )
            return False
    
    def validate_content(self, content: str, file_path: str) -> bool:
        """Validate file content for security issues."""
        try:
            import re
            
            violations_found = []
            
            # Check for suspicious patterns
            for category, patterns in self.suspicious_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        violations_found.append(f"{category}: {pattern}")
            
            if violations_found:
                self._log_violation(
                    SecurityViolationType.SUSPICIOUS_CONTENT,
                    file_path,
                    f"Suspicious patterns found: {', '.join(violations_found)}",
                    "medium"
                )
                
                # Don't block, just warn for now
                logger.warning(f"Suspicious content detected in {file_path}: {violations_found}")
            
            return True  # Allow content but log warnings
            
        except Exception as e:
            logger.error(f"Error validating content: {e}")
            return True  # Default to allow on error
    
    def check_file_permissions(self, file_path: Path, operation: str) -> bool:
        """Check if an operation is allowed on a file."""
        try:
            # Get file permission configuration
            permission = self.file_permissions.get(str(file_path))
            
            if not permission:
                # Default permissions based on file type and location
                permission = self._get_default_permissions(file_path)
            
            # Check if operation is allowed
            if operation not in permission.allowed_operations:
                self._log_violation(
                    SecurityViolationType.PERMISSION_DENIED,
                    str(file_path),
                    f"Operation '{operation}' not allowed",
                    "medium"
                )
                return False
            
            # Check specific restrictions
            if not self._check_restrictions(file_path, operation, permission.restrictions):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking file permissions: {e}")
            return False
    
    def set_file_permissions(self, file_path: Path, permission_level: PermissionLevel,
                           custom_restrictions: Optional[Dict[str, Any]] = None):
        """Set custom permissions for a file."""
        try:
            allowed_operations = self._get_operations_for_level(permission_level)
            restrictions = custom_restrictions or {}
            
            permission = FilePermission(
                path=file_path,
                permission_level=permission_level,
                allowed_operations=allowed_operations,
                restrictions=restrictions
            )
            
            self.file_permissions[str(file_path)] = permission
            logger.info(f"Set permissions for {file_path}: {permission_level.value}")
            
        except Exception as e:
            logger.error(f"Error setting file permissions: {e}")
    
    def get_security_report(self) -> Dict[str, Any]:
        """Generate a security report."""
        try:
            # Count violations by type
            violation_counts = {}
            for violation in self.violations:
                vtype = violation.violation_type.value
                violation_counts[vtype] = violation_counts.get(vtype, 0) + 1
            
            # Recent violations (last 24 hours)
            recent_violations = [
                v for v in self.violations
                if time.time() - v.timestamp < 86400
            ]
            
            # Access statistics
            access_stats = {
                'total_accesses': len(self.access_log),
                'successful_accesses': len([a for a in self.access_log if a['success']]),
                'failed_accesses': len([a for a in self.access_log if not a['success']])
            }
            
            return {
                'workspace_root': str(self.workspace_root),
                'total_violations': len(self.violations),
                'recent_violations': len(recent_violations),
                'violation_counts': violation_counts,
                'access_stats': access_stats,
                'file_permissions_count': len(self.file_permissions),
                'security_level': self._assess_security_level()
            }
            
        except Exception as e:
            logger.error(f"Error generating security report: {e}")
            return {}
    
    def clear_violations(self, older_than_hours: int = 24):
        """Clear old security violations."""
        try:
            cutoff_time = time.time() - (older_than_hours * 3600)
            
            old_count = len(self.violations)
            self.violations = [v for v in self.violations if v.timestamp > cutoff_time]
            new_count = len(self.violations)
            
            logger.info(f"Cleared {old_count - new_count} old violations")
            
        except Exception as e:
            logger.error(f"Error clearing violations: {e}")
    
    def _is_path_allowed(self, path: Path) -> bool:
        """Check if a path is allowed based on security settings."""
        path_str = str(path.resolve())
        
        # Check forbidden paths
        for forbidden in Config.FORBIDDEN_PATHS:
            if path_str.startswith(forbidden):
                return False
        
        # Check hidden files
        if not Config.ALLOW_HIDDEN_FILES:
            if any(part.startswith('.') for part in path.parts):
                return False
        
        # Check system files
        if not Config.ALLOW_SYSTEM_FILES:
            system_indicators = ['system32', 'windows', 'program files', '/bin/', '/sbin/', '/etc/']
            path_lower = path_str.lower()
            if any(indicator in path_lower for indicator in system_indicators):
                return False
        
        return True
    
    def _is_extension_allowed(self, path: Path) -> bool:
        """Check if file extension is allowed."""
        if not Config.ENABLE_FILE_RESTRICTIONS:
            return True
        return path.suffix.lower() in Config.ALLOWED_EXTENSIONS
    
    def _get_default_permissions(self, file_path: Path) -> FilePermission:
        """Get default permissions for a file."""
        # Determine permission level based on file type and location
        if file_path.suffix in ['.py', '.js', '.ts', '.html', '.css']:
            permission_level = PermissionLevel.READ_WRITE
        elif file_path.suffix in ['.json', '.yml', '.yaml', '.xml']:
            permission_level = PermissionLevel.READ_WRITE
        elif file_path.suffix in ['.md', '.txt']:
            permission_level = PermissionLevel.READ_WRITE
        else:
            permission_level = PermissionLevel.READ_ONLY
        
        allowed_operations = self._get_operations_for_level(permission_level)
        
        return FilePermission(
            path=file_path,
            permission_level=permission_level,
            allowed_operations=allowed_operations,
            restrictions={}
        )
    
    def _get_operations_for_level(self, level: PermissionLevel) -> Set[str]:
        """Get allowed operations for a permission level."""
        operations = {
            PermissionLevel.READ_ONLY: {'read'},
            PermissionLevel.READ_WRITE: {'read', 'write'},
            PermissionLevel.FULL_ACCESS: {'read', 'write', 'delete', 'execute'},
            PermissionLevel.RESTRICTED: set()
        }
        return operations.get(level, set())
    
    def _check_restrictions(self, file_path: Path, operation: str, 
                          restrictions: Dict[str, Any]) -> bool:
        """Check operation against specific restrictions."""
        try:
            # Check time-based restrictions
            if 'allowed_hours' in restrictions:
                current_hour = time.localtime().tm_hour
                allowed_hours = restrictions['allowed_hours']
                if current_hour not in allowed_hours:
                    return False
            
            # Check operation count limits
            if 'max_operations_per_hour' in restrictions:
                max_ops = restrictions['max_operations_per_hour']
                recent_ops = [
                    a for a in self.access_log
                    if a['path'] == str(file_path) and 
                       a['operation'] == operation and
                       time.time() - a['timestamp'] < 3600
                ]
                if len(recent_ops) >= max_ops:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking restrictions: {e}")
            return True  # Default to allow on error
    
    def _log_violation(self, violation_type: SecurityViolationType, path: str,
                      description: str, severity: str):
        """Log a security violation."""
        violation = SecurityViolation(
            violation_type=violation_type,
            path=path,
            description=description,
            timestamp=time.time(),
            severity=severity
        )
        
        self.violations.append(violation)
        
        # Log to system logger
        log_level = {
            'low': logging.INFO,
            'medium': logging.WARNING,
            'high': logging.ERROR,
            'critical': logging.CRITICAL
        }.get(severity, logging.WARNING)
        
        logger.log(log_level, f"Security violation: {description}")
    
    def _log_access(self, path: str, operation: str, success: bool):
        """Log file access attempt."""
        access_record = {
            'path': path,
            'operation': operation,
            'success': success,
            'timestamp': time.time()
        }
        
        self.access_log.append(access_record)
        
        # Keep only recent access logs (last 1000 entries)
        if len(self.access_log) > 1000:
            self.access_log = self.access_log[-1000:]
    
    def _assess_security_level(self) -> str:
        """Assess overall security level."""
        try:
            recent_violations = [
                v for v in self.violations
                if time.time() - v.timestamp < 3600  # Last hour
            ]
            
            critical_violations = [v for v in recent_violations if v.severity == 'critical']
            high_violations = [v for v in recent_violations if v.severity == 'high']
            
            if critical_violations:
                return 'critical'
            elif len(high_violations) > 5:
                return 'high_risk'
            elif len(recent_violations) > 10:
                return 'medium_risk'
            else:
                return 'secure'
                
        except Exception as e:
            logger.error(f"Error assessing security level: {e}")
            return 'unknown'
