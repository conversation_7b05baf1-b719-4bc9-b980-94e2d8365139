#!/usr/bin/env python3
"""Test script to verify OpenAI ChatGPT integration."""

import os
from core.ai_manager import AIManager
from core.ai_integration import AnalysisType

def test_openai_integration():
    """Test OpenAI ChatGPT integration."""
    print("🧪 Testing OpenAI ChatGPT integration...")
    
    try:
        # Initialize AI Manager
        ai_manager = AIManager()
        
        print(f"✅ AI Manager initialized")
        print(f"📋 Available providers: {ai_manager.get_available_providers()}")
        print(f"🎯 Default provider: {ai_manager.default_provider}")
        
        # Test 1: Code Analysis with OpenAI
        print("\n🧪 Test 1: Code Analysis with OpenAI")
        
        test_code = '''
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# This is inefficient recursive implementation
result = fibonacci(10)
print(result)
'''
        
        analysis_result = ai_manager.analyze_code(
            code=test_code,
            file_path="test_fibonacci.py",
            analysis_type=AnalysisType.OPTIMIZE
        )
        
        print(f"✅ Analysis completed!")
        print(f"📝 Summary: {analysis_result.summary}")
        print(f"💡 Suggestions: {len(analysis_result.suggestions)}")
        
        for i, suggestion in enumerate(analysis_result.suggestions[:2]):  # Show first 2
            print(f"  {i+1}. {suggestion.type}: {suggestion.description[:100]}...")
        
        # Test 2: Project Generation with OpenAI
        print("\n🧪 Test 2: Project Generation with OpenAI")
        
        project_structure = ai_manager.generate_project_structure(
            "Create a simple Flask web app with user authentication and SQLite database"
        )
        
        print(f"✅ Project structure generated!")
        print(f"📁 Project: {project_structure.get('project_name', 'Unknown')}")
        print(f"🔧 Framework: {project_structure.get('framework', 'Unknown')}")
        print(f"🗂️ Language: {project_structure.get('language', 'Unknown')}")
        print(f"📄 Files: {len(project_structure.get('files', []))}")
        
        # Show some files
        files = project_structure.get('files', [])
        print(f"📋 Generated files:")
        for file_info in files[:3]:  # Show first 3 files
            if isinstance(file_info, dict):
                print(f"  - {file_info.get('path', 'unknown')}: {file_info.get('description', 'No description')}")
        
        # Test 3: Provider Switching
        print("\n🧪 Test 3: Provider Switching")
        
        # Try to switch to Gemini if available
        available_providers = ai_manager.get_available_providers()
        if "gemini" in available_providers:
            print("🔄 Switching to Gemini provider...")
            ai_manager.switch_default_provider("gemini")
            print(f"✅ Switched to: {ai_manager.default_provider}")
            
            # Test with Gemini
            gemini_result = ai_manager.analyze_code(
                code="print('Hello, World!')",
                file_path="hello.py",
                analysis_type=AnalysisType.REVIEW
            )
            print(f"✅ Gemini analysis: {gemini_result.summary[:50]}...")
            
            # Switch back to OpenAI
            ai_manager.switch_default_provider("openai")
            print(f"🔄 Switched back to: {ai_manager.default_provider}")
        else:
            print("ℹ️ Gemini provider not available, skipping switch test")
        
        # Test 4: Best Provider Selection
        print("\n🧪 Test 4: Best Provider Selection")
        
        code_gen_provider = ai_manager.get_best_provider("code_generation")
        print(f"🎯 Best for code generation: {code_gen_provider.value}")
        
        simple_provider = ai_manager.get_best_provider("simple_analysis")
        print(f"⚡ Best for simple analysis: {simple_provider.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_openai_direct():
    """Test OpenAI integration directly."""
    print("\n🧪 Testing OpenAI integration directly...")
    
    try:
        from core.openai_integration import OpenAIIntegration
        
        openai_client = OpenAIIntegration()
        print("✅ OpenAI client initialized")
        
        # Test simple generation
        response = openai_client.generate_content("Write a simple Python function to add two numbers")
        print(f"✅ OpenAI response received: {len(response.text)} characters")
        print(f"📝 Model used: {response.model}")
        print(f"🔢 Tokens used: {response.usage.get('total_tokens', 'Unknown')}")
        print(f"📄 Sample response: {response.text[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct OpenAI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Testing OpenAI ChatGPT Integration")
    print("=" * 50)
    
    # Test direct OpenAI integration first
    direct_success = test_openai_direct()
    
    if direct_success:
        # Test through AI Manager
        manager_success = test_openai_integration()
        
        if manager_success:
            print("\n🎉 All OpenAI integration tests passed!")
            print("✅ OpenAI ChatGPT is ready for code generation")
            print("✅ AI Manager is working with multiple providers")
            print("✅ Provider switching and selection working")
            print("🌐 Web interface will now use OpenAI for better code generation!")
        else:
            print("\n💥 AI Manager tests failed!")
    else:
        print("\n💥 Direct OpenAI tests failed!")
        print("🔧 Check your OPENAI_API_KEY in .env file")
