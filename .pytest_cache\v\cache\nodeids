["tests/test_project_generator.py::TestProjectGenerator::test_analyze_project_requirements_fallback", "tests/test_project_generator.py::TestProjectGenerator::test_analyze_project_requirements_success", "tests/test_project_generator.py::TestProjectGenerator::test_fallback_analysis", "tests/test_project_generator.py::TestProjectGenerator::test_full_project_generation_workflow", "tests/test_project_generator.py::TestProjectGenerator::test_generate_common_files", "tests/test_project_generator.py::TestProjectGenerator::test_generate_flask_files", "tests/test_project_generator.py::TestProjectGenerator::test_generate_package_json", "tests/test_project_generator.py::TestProjectGenerator::test_generate_project_structure", "tests/test_project_generator.py::TestProjectGenerator::test_generate_react_files", "tests/test_project_generator.py::TestProjectGenerator::test_generate_setup_instructions", "tests/test_project_generator.py::TestProjectGenerator::test_project_generation_error_handling", "tests/test_project_generator.py::TestProjectGenerator::test_project_generator_initialization"]