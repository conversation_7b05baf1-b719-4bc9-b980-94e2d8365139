#!/usr/bin/env python3
"""Test script to verify framework string handling fix."""

from core.analysis_engine import Analysis<PERSON>ngine

def test_framework_strings():
    """Test framework handling with string inputs."""
    print("🧪 Testing framework string handling...")
    
    try:
        engine = AnalysisEngine()
        
        # Test 1: React (capitalized)
        print("\n🧪 Test 1: React framework")
        session_id1 = engine.generate_project(
            prompt="Create a React todo app",
            project_name="React Todo",
            preferred_framework="React",  # String input
            target_folder="react_test"
        )
        print(f"✅ React project generated: {session_id1}")
        
        # Test 2: Flask (capitalized)
        print("\n🧪 Test 2: Flask framework")
        session_id2 = engine.generate_project(
            prompt="Create a Flask web app",
            project_name="Flask App",
            preferred_framework="Flask",  # String input
            target_folder="flask_test"
        )
        print(f"✅ Flask project generated: {session_id2}")
        
        # Test 3: Next.js (with dot)
        print("\n🧪 Test 3: Next.js framework")
        session_id3 = engine.generate_project(
            prompt="Create a Next.js app",
            project_name="NextJS App",
            preferred_framework="Next.js",  # String input with dot
            target_folder="nextjs_test"
        )
        print(f"✅ Next.js project generated: {session_id3}")
        
        # Test 4: Angular (capitalized)
        print("\n🧪 Test 4: Angular framework")
        session_id4 = engine.generate_project(
            prompt="Create an Angular app",
            project_name="Angular App",
            preferred_framework="Angular",  # String input
            target_folder="angular_test"
        )
        print(f"✅ Angular project generated: {session_id4}")
        
        # Verify the sessions were created correctly
        print("\n📋 Verifying sessions...")
        
        sessions = [
            (session_id1, "React", "react_test"),
            (session_id2, "Flask", "flask_test"),
            (session_id3, "Next.js", "nextjs_test"),
            (session_id4, "Angular", "angular_test")
        ]
        
        for session_id, framework_name, folder_name in sessions:
            session = engine.get_session(session_id)
            if session and session.change_proposals:
                first_file = session.change_proposals[0].file_diff.file_path
                print(f"✅ {framework_name}: {first_file}")
                
                # Check if folder is correct
                if first_file.startswith(f"{folder_name}/"):
                    print(f"   ✅ Correct folder: {folder_name}")
                else:
                    print(f"   ❌ Wrong folder: expected {folder_name}, got {first_file.split('/')[0]}")
            else:
                print(f"❌ {framework_name}: No session or proposals found")
        
        print("\n🎉 All framework string tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_framework_strings()
    if success:
        print("\n🎉 Framework string handling is working!")
        print("🌐 You can now use the web interface without framework errors!")
    else:
        print("\n💥 Framework string handling test failed!")
