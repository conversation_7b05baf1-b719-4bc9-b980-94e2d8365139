"""Unified AI manager that handles both OpenAI and Gemini integrations."""

import logging
from typing import Dict, Any, Optional, List
from enum import Enum

from config import Config
from .ai_integration import GeminiIntegration, AnalysisType, CodeSuggestion, AnalysisResult
from .openai_integration import OpenAIIntegration

logger = logging.getLogger(__name__)

class AIProvider(Enum):
    """Available AI providers."""
    OPENAI = "openai"
    GEMINI = "gemini"

class AIManager:
    """Unified manager for AI integrations with automatic provider selection."""
    
    def __init__(self):
        """Initialize AI manager with available providers."""
        self.providers = {}
        self.default_provider = Config.DEFAULT_AI_PROVIDER
        
        # Initialize available providers
        self._initialize_providers()
        
        # Set up provider preferences for different tasks
        self._setup_provider_preferences()
        
        logger.info(f"AI Manager initialized with default provider: {self.default_provider}")
    
    def _initialize_providers(self):
        """Initialize available AI providers."""
        # Initialize OpenAI if API key is available
        if Config.OPENAI_API_KEY:
            try:
                self.providers[AIProvider.OPENAI] = OpenAIIntegration()
                logger.info("OpenAI provider initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize OpenAI provider: {e}")
        
        # Initialize Gemini if API key is available
        if Config.GEMINI_API_KEY:
            try:
                self.providers[AIProvider.GEMINI] = GeminiIntegration()
                logger.info("Gemini provider initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize Gemini provider: {e}")
        
        if not self.providers:
            raise RuntimeError("No AI providers could be initialized. Check your API keys.")
    
    def _setup_provider_preferences(self):
        """Set up provider preferences for different tasks."""
        # OpenAI GPT-4 is generally better for code generation and complex analysis
        # Gemini is faster and good for simple analysis tasks
        self.task_preferences = {
            "code_generation": AIProvider.OPENAI,  # GPT-4 excels at code generation
            "project_generation": AIProvider.OPENAI,  # Complex project structure
            "code_review": AIProvider.OPENAI,  # Detailed analysis
            "optimization": AIProvider.OPENAI,  # Performance analysis
            "debugging": AIProvider.OPENAI,  # Bug detection
            "refactoring": AIProvider.OPENAI,  # Code restructuring
            "simple_analysis": AIProvider.GEMINI,  # Fast simple tasks
            "documentation": AIProvider.GEMINI,  # Documentation generation
        }
    
    def get_best_provider(self, task_type: str = "general") -> AIProvider:
        """Get the best provider for a specific task type."""
        # Check task preferences
        preferred = self.task_preferences.get(task_type)
        if preferred and preferred in self.providers:
            return preferred
        
        # Fall back to default provider
        default = AIProvider(self.default_provider)
        if default in self.providers:
            return default
        
        # Use any available provider
        return next(iter(self.providers.keys()))
    
    def analyze_code(self, code: str, file_path: str, analysis_type: AnalysisType) -> AnalysisResult:
        """Analyze code using the best available provider."""
        try:
            # Determine best provider for this analysis type
            task_type = analysis_type.value
            provider_enum = self.get_best_provider(task_type)
            provider = self.providers[provider_enum]
            
            logger.info(f"Using {provider_enum.value} for {analysis_type.value} analysis")
            
            if provider_enum == AIProvider.OPENAI:
                # Use OpenAI provider
                analysis = provider.analyze_code(code, file_path, analysis_type.value)
                return self._convert_openai_to_analysis_result(analysis, analysis_type)
            else:
                # Use Gemini provider (existing interface)
                return provider.analyze_code(code, file_path, analysis_type)
                
        except Exception as e:
            logger.error(f"Code analysis failed with {provider_enum.value}: {e}")
            # Try fallback provider
            return self._try_fallback_analysis(code, file_path, analysis_type, provider_enum)
    
    def generate_project_structure(self, prompt: str) -> Dict[str, Any]:
        """Generate project structure using the best provider for code generation."""
        try:
            # Use OpenAI for project generation (better at code generation)
            provider_enum = self.get_best_provider("project_generation")
            provider = self.providers[provider_enum]
            
            logger.info(f"Using {provider_enum.value} for project generation")
            
            if provider_enum == AIProvider.OPENAI:
                return provider.generate_project_structure(prompt)
            else:
                # Use Gemini's existing interface
                response = provider.model.generate_content(prompt)
                # Parse Gemini response (simplified)
                return self._parse_gemini_project_response(response.text, prompt)
                
        except Exception as e:
            logger.error(f"Project generation failed: {e}")
            # Try fallback
            return self._try_fallback_generation(prompt)
    
    def _convert_openai_to_analysis_result(self, openai_analysis: Dict[str, Any], analysis_type: AnalysisType) -> AnalysisResult:
        """Convert OpenAI analysis result to AnalysisResult format."""
        suggestions = []
        
        for suggestion in openai_analysis.get("suggestions", []):
            code_suggestion = CodeSuggestion(
                type=AnalysisType.REVIEW,  # Default type
                title=suggestion.get("title", "AI Suggestion"),
                description=suggestion.get("description", ""),
                original_code="",  # Will be filled by caller if needed
                suggested_code=suggestion.get("code_change", ""),
                confidence=suggestion.get("confidence", 0.8),
                reasoning=suggestion.get("reasoning", ""),
                file_path=""  # Will be filled by caller
            )
            suggestions.append(code_suggestion)
        
        return AnalysisResult(
            suggestions=suggestions,
            summary=openai_analysis.get("summary", "Analysis completed"),
            analysis_time=0.0  # Will be set by caller
        )
    
    def _try_fallback_analysis(self, code: str, file_path: str, analysis_type: AnalysisType, failed_provider: AIProvider) -> AnalysisResult:
        """Try fallback provider for analysis."""
        try:
            # Try the other provider
            fallback_providers = [p for p in self.providers.keys() if p != failed_provider]
            if fallback_providers:
                fallback_provider = fallback_providers[0]
                provider = self.providers[fallback_provider]
                
                logger.info(f"Trying fallback provider: {fallback_provider.value}")
                
                if fallback_provider == AIProvider.OPENAI:
                    analysis = provider.analyze_code(code, file_path, analysis_type.value)
                    return self._convert_openai_to_analysis_result(analysis, analysis_type)
                else:
                    return provider.analyze_code(code, file_path, analysis_type)
            
        except Exception as e:
            logger.error(f"Fallback analysis also failed: {e}")
        
        # Return minimal result
        return AnalysisResult(
            suggestions=[],
            summary="Analysis failed - please check your API keys and try again",
            analysis_time=0.0
        )
    
    def _try_fallback_generation(self, prompt: str) -> Dict[str, Any]:
        """Try fallback provider for project generation."""
        try:
            # Try any available provider
            for provider_enum, provider in self.providers.items():
                try:
                    if provider_enum == AIProvider.GEMINI:
                        response = provider.model.generate_content(prompt)
                        return self._parse_gemini_project_response(response.text, prompt)
                    else:
                        return provider.generate_project_structure(prompt)
                except Exception as e:
                    logger.warning(f"Provider {provider_enum.value} failed: {e}")
                    continue
        except Exception as e:
            logger.error(f"All providers failed for project generation: {e}")
        
        # Return minimal fallback
        return {
            "project_name": "fallback_project",
            "description": f"Fallback project for: {prompt}",
            "project_type": "web_app",
            "framework": "vanilla",
            "language": "python",
            "files": [
                {
                    "path": "main.py",
                    "content": f'"""\n{prompt}\n"""\n\nprint("Hello, World!")\n',
                    "description": "Main file"
                }
            ],
            "directories": [],
            "dependencies": {"pip": [], "npm": []},
            "setup_instructions": ["Run: python main.py"],
            "features": ["basic_functionality"]
        }
    
    def _parse_gemini_project_response(self, response_text: str, prompt: str) -> Dict[str, Any]:
        """Parse Gemini response for project generation."""
        # Simplified parsing - in practice, you'd want more sophisticated parsing
        return {
            "project_name": "gemini_project",
            "description": f"Project generated by Gemini: {prompt}",
            "project_type": "web_app",
            "framework": "vanilla",
            "language": "python",
            "files": [
                {
                    "path": "main.py",
                    "content": f'"""\n{prompt}\n\nGenerated by Gemini\n"""\n\nprint("Hello from Gemini!")\n',
                    "description": "Main application file"
                }
            ],
            "directories": ["src"],
            "dependencies": {"pip": [], "npm": []},
            "setup_instructions": ["Run: python main.py"],
            "features": ["basic_functionality"]
        }
    
    def get_available_providers(self) -> List[str]:
        """Get list of available providers."""
        return [provider.value for provider in self.providers.keys()]
    
    def switch_default_provider(self, provider: str):
        """Switch the default provider."""
        if provider in ["openai", "gemini"]:
            provider_enum = AIProvider(provider)
            if provider_enum in self.providers:
                self.default_provider = provider
                logger.info(f"Switched default provider to: {provider}")
            else:
                raise ValueError(f"Provider {provider} is not available")
        else:
            raise ValueError(f"Invalid provider: {provider}")
