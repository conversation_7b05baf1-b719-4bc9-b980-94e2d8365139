"""Streamlit web interface for the AI Code Assistant."""

import streamlit as st
import os
import time
from pathlib import Path
from typing import List, Optional

# Import core components
from core.analysis_engine import AnalysisEngine
from core.ai_integration import AnalysisType
from core.diff_manager import ChangeStatus
from config import Config

# Page configuration
st.set_page_config(
    page_title="AI Code Assistant",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'analysis_engine' not in st.session_state:
    try:
        st.session_state.analysis_engine = AnalysisEngine()
        st.session_state.initialized = True
    except Exception as e:
        st.session_state.initialized = False
        st.session_state.init_error = str(e)

if 'selected_file' not in st.session_state:
    st.session_state.selected_file = None

if 'current_session' not in st.session_state:
    st.session_state.current_session = None

def main():
    """Main application interface."""
    
    # Header
    st.title("🤖 AI Code Assistant")
    st.markdown("*Powered by Google Gemini 1.5 Flash*")
    
    # Check initialization
    if not st.session_state.get('initialized', False):
        st.error(f"❌ Initialization failed: {st.session_state.get('init_error', 'Unknown error')}")
        st.info("Please check your configuration and API key.")
        return
    
    # Sidebar
    with st.sidebar:
        st.header("📁 Project Explorer")
        
        # Workspace info
        workspace_root = st.session_state.analysis_engine.file_manager.workspace_root
        st.info(f"**Workspace:** `{workspace_root}`")
        
        # File browser
        selected_file = file_browser()
        
        if selected_file:
            st.session_state.selected_file = selected_file
        
        st.divider()
        
        # Analysis options
        st.header("🔍 Analysis Options")
        analysis_type = st.selectbox(
            "Analysis Type",
            options=[t.value for t in AnalysisType],
            format_func=lambda x: x.replace('_', ' ').title()
        )
        
        # Quick actions
        st.header("⚡ Quick Actions")
        
        if st.button("📊 Project Summary", use_container_width=True):
            show_project_summary()
        
        if st.button("📋 Pending Changes", use_container_width=True):
            show_pending_changes()

        if st.button("🚀 Generate Project", use_container_width=True):
            show_project_generation()

        if st.button("💻 Terminal", use_container_width=True):
            st.session_state.show_terminal = True

        if st.button("📁 Project Manager", use_container_width=True):
            st.session_state.show_project_manager = True
    
    # Main content area
    if st.session_state.get('show_terminal', False):
        show_terminal()
    elif st.session_state.get('show_project_manager', False):
        show_project_manager()
    elif analysis_type == "project_generation":
        show_project_generation()
    elif st.session_state.selected_file:
        show_file_analysis(st.session_state.selected_file, AnalysisType(analysis_type))
    else:
        show_welcome_screen()

def file_browser() -> Optional[str]:
    """File browser component."""
    try:
        engine = st.session_state.analysis_engine
        files = engine.file_manager.list_files(recursive=True)
        
        # Filter for text files only
        text_files = [f for f in files if f.is_text and not f.is_directory]
        
        if not text_files:
            st.warning("No text files found in workspace")
            return None
        
        # Group files by directory
        file_groups = {}
        for file_info in text_files:
            dir_name = str(file_info.path.parent) if file_info.path.parent != Path('.') else "Root"
            if dir_name not in file_groups:
                file_groups[dir_name] = []
            file_groups[dir_name].append(file_info)
        
        # Display files by group
        selected_file = None
        for dir_name, files_in_dir in sorted(file_groups.items()):
            with st.expander(f"📁 {dir_name}", expanded=(dir_name == "Root")):
                for file_info in sorted(files_in_dir, key=lambda f: f.name):
                    file_display = f"{file_info.name} ({file_info.size} bytes)"
                    if st.button(file_display, key=f"file_{file_info.path}"):
                        selected_file = str(file_info.path)
        
        return selected_file
        
    except Exception as e:
        st.error(f"Error browsing files: {e}")
        return None

def show_welcome_screen():
    """Show welcome screen when no file is selected."""
    st.markdown("""
    ## Welcome to AI Code Assistant! 👋
    
    This intelligent code assistant helps you analyze, improve, and maintain your code using AI.
    
    ### Getting Started:
    1. **Select a file** from the sidebar to begin analysis
    2. **Choose an analysis type** (review, refactor, optimize, etc.)
    3. **Review AI suggestions** with diff visualization
    4. **Accept or reject changes** with one click
    
    ### Features:
    - 🔍 **Code Review**: Get comprehensive code quality feedback
    - 🔄 **Refactoring**: Improve code structure and maintainability
    - ⚡ **Optimization**: Enhance performance and efficiency
    - 📝 **Documentation**: Generate comprehensive code documentation
    - 🐛 **Debugging**: Identify and fix potential issues
    - ✨ **Code Completion**: Complete partial implementations
    
    ### Security:
    - All operations are confined to your workspace directory
    - Changes require explicit approval before being applied
    - Full audit trail of all modifications
    """)
    
    # Show project summary
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📊 Project Overview")
        try:
            summary = st.session_state.analysis_engine.get_project_summary()
            
            if summary.get('project_type'):
                st.info(f"**Project Type:** {summary['project_type']}")
            
            file_stats = summary.get('file_stats', {})
            if file_stats:
                st.metric("Total Files", file_stats.get('total_files', 0))
                st.metric("Directories", file_stats.get('total_directories', 0))
        except Exception as e:
            st.error(f"Error loading project summary: {e}")
    
    with col2:
        st.subheader("📈 Recent Activity")
        try:
            engine = st.session_state.analysis_engine
            recent_sessions = engine.get_recent_sessions(5)
            
            if recent_sessions:
                for session in recent_sessions:
                    with st.container():
                        st.write(f"**{session.file_path}** - {session.analysis_type.value}")
                        st.caption(f"{len(session.change_proposals)} suggestions • {time.strftime('%H:%M', time.localtime(session.created_at))}")
            else:
                st.info("No recent analysis sessions")
        except Exception as e:
            st.error(f"Error loading recent activity: {e}")

def show_file_analysis(file_path: str, analysis_type: AnalysisType):
    """Show file analysis interface."""
    st.header(f"📄 {file_path}")
    
    # File info
    try:
        engine = st.session_state.analysis_engine
        file_info = engine.file_manager.get_file_info(file_path)
        
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Size", f"{file_info.size} bytes")
        with col2:
            st.metric("Type", file_info.extension or "No extension")
        with col3:
            st.metric("Modified", time.strftime('%Y-%m-%d', time.localtime(file_info.last_modified)))
        with col4:
            if st.button("🔍 Analyze", type="primary"):
                analyze_file(file_path, analysis_type)
        
    except Exception as e:
        st.error(f"Error loading file info: {e}")
        return
    
    # Show current analysis session if exists
    if st.session_state.current_session:
        show_analysis_results(st.session_state.current_session)
    
    # Show file content
    with st.expander("📖 File Content", expanded=False):
        try:
            content = engine.file_manager.read_file(file_path)
            st.code(content, language=get_language_from_extension(file_info.extension))
        except Exception as e:
            st.error(f"Error reading file: {e}")

def analyze_file(file_path: str, analysis_type: AnalysisType):
    """Perform file analysis."""
    try:
        with st.spinner(f"Analyzing {file_path} with {analysis_type.value}..."):
            engine = st.session_state.analysis_engine
            session = engine.analyze_file(file_path, analysis_type)
            st.session_state.current_session = session
            st.success(f"Analysis completed! Found {len(session.change_proposals)} suggestions.")
            st.rerun()
    except Exception as e:
        st.error(f"Analysis failed: {e}")

def show_analysis_results(session):
    """Show analysis results and change proposals."""
    st.subheader("🎯 Analysis Results")
    
    # Summary
    st.info(f"**Summary:** {session.ai_result.summary}")
    
    # Metrics
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Suggestions", len(session.change_proposals))
    with col2:
        st.metric("Analysis Time", f"{session.ai_result.analysis_time:.2f}s")
    with col3:
        confidence_avg = sum(p.confidence for p in session.change_proposals) / len(session.change_proposals) if session.change_proposals else 0
        st.metric("Avg Confidence", f"{confidence_avg:.1%}")
    
    # Change proposals
    if session.change_proposals:
        st.subheader("💡 Suggested Changes")
        
        for i, proposal in enumerate(session.change_proposals):
            show_change_proposal(proposal, i)
    else:
        st.info("No change suggestions generated.")

def show_change_proposal(proposal, index: int):
    """Show a single change proposal with accept/reject buttons."""
    with st.container():
        st.markdown(f"### {index + 1}. {proposal.title}")
        
        # Metadata
        col1, col2, col3 = st.columns(3)
        with col1:
            st.caption(f"**Type:** {proposal.suggestion_type}")
        with col2:
            st.caption(f"**Confidence:** {proposal.confidence:.1%}")
        with col3:
            status_color = {"pending": "🟡", "accepted": "🟢", "rejected": "🔴", "applied": "✅"}
            st.caption(f"**Status:** {status_color.get(proposal.status.value, '⚪')} {proposal.status.value}")
        
        # Description
        st.write(proposal.description)
        
        if proposal.reasoning:
            with st.expander("🧠 AI Reasoning"):
                st.write(proposal.reasoning)
        
        # Diff visualization
        with st.expander("🔍 View Changes", expanded=True):
            diff_display = st.session_state.analysis_engine.diff_manager.format_diff_for_display(proposal.file_diff)
            st.code(diff_display, language="diff")
        
        # Action buttons
        if proposal.status == ChangeStatus.PENDING:
            col1, col2, col3 = st.columns([1, 1, 4])
            
            with col1:
                if st.button("✅ Accept", key=f"accept_{proposal.id}"):
                    accept_change(proposal.id)
            
            with col2:
                if st.button("❌ Reject", key=f"reject_{proposal.id}"):
                    reject_change(proposal.id)
        
        elif proposal.status == ChangeStatus.ACCEPTED:
            if st.button("🚀 Apply Changes", key=f"apply_{proposal.id}", type="primary"):
                apply_change(proposal.id)
        
        st.divider()

def accept_change(change_id: str):
    """Accept a change proposal."""
    try:
        engine = st.session_state.analysis_engine
        engine.diff_manager.accept_change(change_id)
        st.success("Change accepted! Click 'Apply Changes' to save to file.")
        st.rerun()
    except Exception as e:
        st.error(f"Error accepting change: {e}")

def reject_change(change_id: str):
    """Reject a change proposal."""
    try:
        engine = st.session_state.analysis_engine
        engine.diff_manager.reject_change(change_id)
        st.success("Change rejected.")
        st.rerun()
    except Exception as e:
        st.error(f"Error rejecting change: {e}")

def apply_change(change_id: str):
    """Apply an accepted change."""
    try:
        engine = st.session_state.analysis_engine
        engine.diff_manager.apply_change(change_id, engine.file_manager)
        st.success("Changes applied to file!")
        st.rerun()
    except Exception as e:
        st.error(f"Error applying change: {e}")

def show_project_summary():
    """Show project summary in a modal."""
    try:
        summary = st.session_state.analysis_engine.get_project_summary()
        
        st.subheader("📊 Project Summary")
        
        if summary.get('project_type'):
            st.info(f"**Project Type:** {summary['project_type']}")
        
        file_stats = summary.get('file_stats', {})
        if file_stats:
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Total Files", file_stats.get('total_files', 0))
            with col2:
                st.metric("Directories", file_stats.get('total_directories', 0))
            
            # File type breakdown
            file_types = file_stats.get('file_types', {})
            if file_types:
                st.subheader("📁 File Types")
                for ext, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True):
                    st.write(f"**{ext}**: {count} files")
        
    except Exception as e:
        st.error(f"Error loading project summary: {e}")

def show_pending_changes():
    """Show all pending changes."""
    try:
        engine = st.session_state.analysis_engine
        pending_changes = engine.diff_manager.get_pending_changes()

        st.subheader("📋 Pending Changes")

        if pending_changes:
            for proposal in pending_changes:
                with st.container():
                    st.write(f"**{proposal.title}** - `{proposal.file_diff.file_path}`")
                    st.caption(f"Confidence: {proposal.confidence:.1%} • Type: {proposal.suggestion_type}")

                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("✅ Accept", key=f"pending_accept_{proposal.id}"):
                            accept_change(proposal.id)
                    with col2:
                        if st.button("❌ Reject", key=f"pending_reject_{proposal.id}"):
                            reject_change(proposal.id)

                    st.divider()
        else:
            st.info("No pending changes.")

    except Exception as e:
        st.error(f"Error loading pending changes: {e}")

def get_language_from_extension(extension: str) -> str:
    """Get syntax highlighting language from file extension."""
    language_map = {
        '.py': 'python',
        '.js': 'javascript',
        '.ts': 'typescript',
        '.html': 'html',
        '.css': 'css',
        '.json': 'json',
        '.xml': 'xml',
        '.sql': 'sql',
        '.sh': 'bash',
        '.bat': 'batch',
        '.md': 'markdown',
        '.yml': 'yaml',
        '.yaml': 'yaml'
    }
    return language_map.get(extension.lower(), 'text')

def show_project_generation():
    """Show project generation interface."""
    st.subheader("🚀 Generate New Project")

    with st.form("project_generation_form"):
        st.markdown("### Project Description")
        project_prompt = st.text_area(
            "Describe the project you want to generate:",
            placeholder="e.g., 'Create a Flask web app with user authentication and SQLite database'",
            height=100
        )

        col1, col2 = st.columns(2)

        with col1:
            project_name = st.text_input(
                "Project Name (optional):",
                placeholder="my-awesome-project"
            )

            preferred_language = st.selectbox(
                "Preferred Language:",
                ["Auto-detect", "Python", "JavaScript", "TypeScript", "Java", "C#", "Go", "Rust"],
                index=0
            )

        with col2:
            preferred_framework = st.selectbox(
                "Preferred Framework:",
                ["Auto-detect", "Flask", "Django", "FastAPI", "React", "Angular", "Vue", "Express", "Next.js"],
                index=0
            )

        # AI Provider Selection
        st.markdown("### 🧠 AI Provider")
        col1, col2 = st.columns(2)

        with col1:
            # Get available providers
            available_providers = st.session_state.analysis_engine.ai_manager.get_available_providers()
            current_provider = st.session_state.analysis_engine.ai_manager.default_provider

            provider_options = ["Auto"] + [p.title() for p in available_providers]
            current_index = 0  # Default to Auto

            if current_provider in available_providers:
                current_index = provider_options.index(current_provider.title())

            selected_provider = st.selectbox(
                "AI Model:",
                provider_options,
                index=current_index,
                help="Choose AI provider for code generation"
            )

        with col2:
            # Show provider info
            if selected_provider == "Auto":
                st.info("🎯 **Auto Mode**: Uses best AI for each task\n- OpenAI for code generation\n- Gemini for fast analysis")
            elif selected_provider.lower() == "openai":
                st.info("🤖 **OpenAI ChatGPT**: Best for complex code generation and detailed analysis")
            elif selected_provider.lower() == "gemini":
                st.info("⚡ **Google Gemini**: Fast analysis and simple tasks")

            # Apply provider change if different
            if selected_provider != "Auto" and selected_provider.lower() != current_provider:
                if st.button("🔄 Switch Provider", key="switch_provider_btn"):
                    try:
                        st.session_state.analysis_engine.ai_manager.switch_default_provider(selected_provider.lower())
                        st.success(f"✅ Switched to {selected_provider}")
                        st.rerun()
                    except Exception as e:
                        st.error(f"❌ Failed to switch: {e}")

            include_options = st.multiselect(
                "Include:",
                ["Tests", "Documentation", "Docker", "CI/CD", "Database"],
                default=["Tests", "Documentation"]
            )

        # Project folder options
        st.markdown("### 📁 Project Folder")
        col1, col2 = st.columns(2)

        with col1:
            create_folder = st.checkbox(
                "Create separate project folder",
                value=True,
                help="Creates a dedicated folder for this project"
            )

        with col2:
            folder_name = ""
            if create_folder:
                folder_name = st.text_input(
                    "Folder name (optional):",
                    placeholder="Will use project name if empty",
                    help="Leave empty to auto-generate from project name"
                )

        submitted = st.form_submit_button("🎯 Generate Project", type="primary")

        if submitted and project_prompt:
            # Store form data in session state
            target_folder = None
            if create_folder:
                if folder_name and folder_name.strip():
                    target_folder = folder_name.strip()
                elif project_name and project_name.strip():
                    target_folder = project_name.strip()
                # If neither, let the engine auto-generate



            # Handle AI provider selection
            if selected_provider != "Auto":
                # Temporarily switch provider for this generation
                original_provider = st.session_state.analysis_engine.ai_manager.default_provider
                if selected_provider.lower() != original_provider:
                    st.session_state.analysis_engine.ai_manager.switch_default_provider(selected_provider.lower())
                    st.info(f"🔄 Using {selected_provider} for this project generation")

            st.session_state.project_form_data = {
                'prompt': project_prompt,
                'project_name': project_name if project_name else None,
                'preferred_framework': preferred_framework if preferred_framework != "Auto-detect" else None,
                'preferred_language': preferred_language if preferred_language != "Auto-detect" else None,
                'include_tests': "Tests" in include_options,
                'include_docs': "Documentation" in include_options,
                'target_folder': target_folder,
                'ai_provider': selected_provider
            }
            st.session_state.generate_project_trigger = True
        elif submitted:
            st.error("Please provide a project description.")

    # Handle project generation outside the form
    if st.session_state.get('generate_project_trigger', False):
        form_data = st.session_state.project_form_data
        generate_project_from_prompt(
            form_data['prompt'],
            form_data['project_name'],
            form_data['preferred_framework'],
            form_data['preferred_language'],
            form_data['include_tests'],
            form_data['include_docs'],
            form_data['target_folder']
        )
        # Clear the trigger
        st.session_state.generate_project_trigger = False

def generate_project_from_prompt(prompt: str, project_name: str = None,
                                framework: str = None, language: str = None,
                                include_tests: bool = True, include_docs: bool = True,
                                target_folder: str = None):
    """Generate project from user prompt."""
    try:
        with st.spinner("🤖 Generating project structure..."):
            engine = st.session_state.analysis_engine
            session_id = engine.generate_project(
                prompt=prompt,
                project_name=project_name,
                preferred_framework=framework,
                preferred_language=language,
                include_tests=include_tests,
                include_docs=include_docs,
                target_folder=target_folder
            )

            # Store session ID for later use
            st.session_state.current_project_session = session_id

        st.success("✅ Project generated successfully!")

        # Show project preview
        preview = engine.get_project_structure_preview(session_id)
        if preview:
            show_project_preview(preview, session_id)

    except Exception as e:
        st.error(f"❌ Project generation failed: {e}")

def show_project_preview(preview: dict, session_id: str):
    """Show generated project preview."""
    st.subheader("📋 Project Preview")

    # Project info
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Project Name", preview['project_name'])
    with col2:
        st.metric("Total Files", preview['total_files'])
    with col3:
        st.metric("Dependencies", len(preview.get('dependencies', [])))

    # Project summary
    st.info(f"**Summary:** {preview['summary']}")

    # File structure
    st.subheader("📂 Project Structure")

    # Create expandable sections for each directory
    for dir_idx, (directory, files) in enumerate(preview['directories'].items()):
        with st.expander(f"📁 {directory}/ ({len(files)} files)", expanded=True):
            for file_idx, file_info in enumerate(files):
                col1, col2, col3, col4 = st.columns([3, 1, 2, 1])

                with col1:
                    st.write(f"📄 **{file_info['name']}**")
                with col2:
                    st.caption(f"{file_info['size']/1024:.1f}KB")
                with col3:
                    st.caption(file_info['description'])
                with col4:
                    # Use unique key combining session_id, directory index, and file index
                    unique_key = f"view_{session_id}_{dir_idx}_{file_idx}_{file_info['change_id'][:8]}"
                    if st.button("👁️", key=unique_key, help="Preview file"):
                        st.session_state.preview_file_id = file_info['change_id']

    # Dependencies
    if preview.get('dependencies'):
        st.subheader("📦 Dependencies")
        deps_text = ", ".join(preview['dependencies'])
        st.code(deps_text, language="text")

    # Show file preview if requested
    if st.session_state.get('preview_file_id'):
        st.divider()
        show_file_preview(st.session_state.preview_file_id)
        if st.button("❌ Close Preview", key=f"close_preview_{session_id}"):
            del st.session_state.preview_file_id
            st.rerun()

    # Action buttons
    st.subheader("🎯 Actions")
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("✅ Accept All Files", type="primary", key=f"accept_all_{session_id}"):
            st.write("🔥 **BUTTON CLICKED!**")
            st.write(f"Session ID: {session_id}")
            st.write(f"Session ID type: {type(session_id)}")
            apply_project_generation(session_id)

    with col2:
        if st.button("🔍 Customize Selection", key=f"customize_{session_id}"):
            show_file_selection_interface(session_id, preview)

    with col3:
        if st.button("❌ Reject Project", key=f"reject_{session_id}"):
            reject_project_generation(session_id)

def show_file_preview(change_id: str):
    """Show preview of a specific file."""
    try:
        engine = st.session_state.analysis_engine
        proposal = engine.diff_manager.get_change_proposal(change_id)

        if proposal:
            st.subheader(f"📄 {proposal.file_diff.file_path}")
            st.caption(proposal.description)

            # Determine language for syntax highlighting
            file_ext = Path(proposal.file_diff.file_path).suffix
            language = get_language_from_extension(file_ext)

            st.code(proposal.file_diff.new_content, language=language)
        else:
            st.error("File not found")

    except Exception as e:
        st.error(f"Error previewing file: {e}")

def show_file_selection_interface(session_id: str, preview: dict):
    """Show interface for selecting which files to create."""
    st.subheader("🎯 Select Files to Create")

    selected_files = []

    for dir_idx, (directory, files) in enumerate(preview['directories'].items()):
        st.write(f"**📁 {directory}/**")

        for file_idx, file_info in enumerate(files):
            col1, col2 = st.columns([1, 4])

            with col1:
                # Use unique key combining session_id, directory index, and file index
                unique_key = f"select_{session_id}_{dir_idx}_{file_idx}_{file_info['change_id'][:8]}"
                selected = st.checkbox(
                    "",
                    value=True,
                    key=unique_key
                )
                if selected:
                    selected_files.append(file_info['path'])

            with col2:
                st.write(f"📄 {file_info['name']} - {file_info['description']}")

    if st.button("✅ Create Selected Files", type="primary", key=f"create_selected_{session_id}"):
        if selected_files:
            apply_project_generation(session_id, selected_files)
        else:
            st.warning("Please select at least one file to create.")

def apply_project_generation(session_id: str, selected_files: list = None):
    """Apply project generation by creating files."""
    try:
        engine = st.session_state.analysis_engine

        # Debug: Show what we're working with
        st.write(f"🔍 **Debug Info:**")
        st.write(f"- Session ID: `{session_id}`")
        st.write(f"- Selected files: `{selected_files}`")

        # Check session exists
        session = engine.get_session(session_id)
        if not session:
            st.error(f"❌ Session {session_id} not found!")
            st.write(f"Available sessions: {list(engine.analysis_sessions.keys())}")
            return

        st.write(f"- Session found: ✅")
        st.write(f"- Proposals count: {len(session.change_proposals)}")

        # Show proposals
        for i, proposal in enumerate(session.change_proposals):
            st.write(f"  {i+1}. `{proposal.file_diff.file_path}` ({proposal.status})")

        with st.spinner("📁 Creating project files..."):
            # Check workspace before
            workspace_root = engine.file_manager.workspace_root
            st.write(f"- Workspace root: `{workspace_root}`")

            success = engine.apply_project_generation(session_id, selected_files)

            st.write(f"- Apply result: `{success}`")

        if success:
            file_count = len(selected_files) if selected_files else "all"
            st.success(f"✅ Successfully created {file_count} project files!")
            st.balloons()

            # Check what was actually created
            workspace_root = engine.file_manager.workspace_root

            # Try to find the project folder
            project_folders = []
            for proposal in session.change_proposals:
                file_path = proposal.file_diff.file_path
                if '/' in file_path:
                    folder = file_path.split('/')[0]
                    if folder not in project_folders:
                        project_folders.append(folder)

            if project_folders:
                for folder in project_folders:
                    folder_path = workspace_root / folder
                    if folder_path.exists():
                        files = list(folder_path.glob("*"))
                        st.success(f"📁 Created folder: {folder} with {len(files)} files")
                        for file_path in files[:5]:  # Show first 5 files
                            if file_path.is_file():
                                st.write(f"  - {file_path.name} ({file_path.stat().st_size} bytes)")
                    else:
                        st.warning(f"📁 Folder not found: {folder}")

            # Show next steps
            st.info("""
            **Next Steps:**
            1. Navigate to your workspace directory
            2. Follow the setup instructions in README.md
            3. Start developing your project!
            """)
        else:
            st.error("❌ Failed to create project files")

    except Exception as e:
        st.error(f"Error creating project: {e}")
        import traceback
        st.code(traceback.format_exc())

def reject_project_generation(session_id: str):
    """Reject the generated project."""
    try:
        engine = st.session_state.analysis_engine
        session = engine.get_session(session_id)

        if session:
            # Reject all change proposals
            for proposal in session.change_proposals:
                engine.diff_manager.reject_change(proposal.id, "User rejected project")

            st.success("❌ Project generation rejected")
        else:
            st.error("Session not found")

    except Exception as e:
        st.error(f"Error rejecting project: {e}")

def show_terminal():
    """Show terminal interface for running commands."""
    st.header("💻 Terminal")

    # Terminal controls
    col1, col2, col3 = st.columns([3, 1, 1])

    with col1:
        # Show current working directory
        workspace_root = st.session_state.analysis_engine.file_manager.workspace_root
        project_dir = st.session_state.get('terminal_project_dir')

        if project_dir:
            current_dir = workspace_root / project_dir
            st.info(f"**Working Directory:** `{current_dir}` (Project: {project_dir})")
        else:
            st.info(f"**Working Directory:** `{workspace_root}` (Workspace Root)")

    with col2:
        if st.button("🔄 Clear History"):
            st.session_state.terminal_history = []
            st.rerun()

    with col3:
        if st.button("❌ Close Terminal"):
            st.session_state.show_terminal = False
            st.rerun()

    # Initialize terminal history
    if 'terminal_history' not in st.session_state:
        st.session_state.terminal_history = []

    # Command input
    st.subheader("📝 Run Command")

    with st.form("terminal_form"):
        command = st.text_input(
            "Command:",
            placeholder="e.g., python main.py, npm install, ls, dir",
            help="Enter a command to run in the workspace directory"
        )

        col1, col2 = st.columns([1, 4])
        with col1:
            submitted = st.form_submit_button("▶️ Run", type="primary")
        with col2:
            show_commands = st.form_submit_button("📋 Common Commands")

    if submitted and command:
        run_terminal_command(command)

    if show_commands:
        show_common_commands()

    # Terminal output history
    st.subheader("📜 Command History")

    if st.session_state.terminal_history:
        # Show recent commands first
        for i, entry in enumerate(reversed(st.session_state.terminal_history[-10:])):
            with st.expander(f"💻 {entry['command']}", expanded=(i == 0)):
                st.code(f"$ {entry['command']}", language="bash")

                if entry['success']:
                    st.success(f"✅ Completed in {entry['duration']:.2f}s")
                    if entry['output']:
                        st.code(entry['output'], language="text")
                else:
                    st.error(f"❌ Failed (exit code: {entry['exit_code']})")
                    if entry['output']:
                        st.code(entry['output'], language="text")

                st.caption(f"Executed at: {entry['timestamp']}")
    else:
        st.info("No commands executed yet. Enter a command above to get started!")

def run_terminal_command(command: str):
    """Execute a terminal command and show results."""
    import subprocess
    import time

    try:
        workspace_root = st.session_state.analysis_engine.file_manager.workspace_root
        project_dir = st.session_state.get('terminal_project_dir')

        # Determine working directory
        if project_dir:
            working_dir = workspace_root / project_dir
            if not working_dir.exists():
                st.error(f"Project directory '{project_dir}' does not exist")
                return
        else:
            working_dir = workspace_root

        with st.spinner(f"Running: {command}"):
            start_time = time.time()

            # Run command in the appropriate directory
            result = subprocess.run(
                command,
                shell=True,
                cwd=working_dir,
                capture_output=True,
                text=True,
                timeout=30  # 30 second timeout
            )

            duration = time.time() - start_time

            # Combine stdout and stderr
            output = ""
            if result.stdout:
                output += result.stdout
            if result.stderr:
                if output:
                    output += "\n--- STDERR ---\n"
                output += result.stderr

            # Store in history
            entry = {
                'command': command,
                'output': output,
                'exit_code': result.returncode,
                'success': result.returncode == 0,
                'duration': duration,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }

            st.session_state.terminal_history.append(entry)

            # Show immediate feedback
            if result.returncode == 0:
                st.success(f"✅ Command completed successfully in {duration:.2f}s")
            else:
                st.error(f"❌ Command failed with exit code {result.returncode}")

            st.rerun()

    except subprocess.TimeoutExpired:
        st.error("❌ Command timed out after 30 seconds")
    except Exception as e:
        st.error(f"❌ Error running command: {e}")

def show_common_commands():
    """Show common commands for different project types."""
    st.subheader("📋 Common Commands")

    # Python commands
    with st.expander("🐍 Python Commands"):
        python_commands = [
            "python --version",
            "pip list",
            "pip install -r requirements.txt",
            "python main.py",
            "python -m pytest",
            "python -m venv venv",
            "pip freeze > requirements.txt"
        ]
        for cmd in python_commands:
            if st.button(f"▶️ {cmd}", key=f"py_{cmd}"):
                st.session_state.terminal_command = cmd
                st.rerun()

    # Node.js commands
    with st.expander("📦 Node.js Commands"):
        node_commands = [
            "node --version",
            "npm --version",
            "npm install",
            "npm start",
            "npm run build",
            "npm test",
            "npm list"
        ]
        for cmd in node_commands:
            if st.button(f"▶️ {cmd}", key=f"node_{cmd}"):
                st.session_state.terminal_command = cmd
                st.rerun()

    # File system commands
    with st.expander("📁 File System Commands"):
        # Windows and Unix compatible commands
        fs_commands = [
            "dir" if st.session_state.get('os_type', 'windows') == 'windows' else "ls -la",
            "tree /f" if st.session_state.get('os_type', 'windows') == 'windows' else "tree",
            "type README.md" if st.session_state.get('os_type', 'windows') == 'windows' else "cat README.md",
            "cd ..",
            "pwd" if st.session_state.get('os_type', 'windows') != 'windows' else "cd"
        ]
        for cmd in fs_commands:
            if st.button(f"▶️ {cmd}", key=f"fs_{cmd}"):
                st.session_state.terminal_command = cmd
                st.rerun()

    # Git commands
    with st.expander("🔧 Git Commands"):
        git_commands = [
            "git status",
            "git log --oneline -10",
            "git diff",
            "git add .",
            "git commit -m 'Update'",
            "git branch",
            "git remote -v"
        ]
        for cmd in git_commands:
            if st.button(f"▶️ {cmd}", key=f"git_{cmd}"):
                st.session_state.terminal_command = cmd
                st.rerun()

def show_project_manager():
    """Show project manager interface."""
    st.header("📁 Project Manager")

    # Project manager controls
    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        st.info("**Manage your projects:** View, select, and delete project folders")

    with col2:
        if st.button("🔄 Refresh"):
            st.rerun()

    with col3:
        if st.button("❌ Close Manager"):
            st.session_state.show_project_manager = False
            st.rerun()

    # Get project folders
    try:
        engine = st.session_state.analysis_engine
        project_folders = engine.list_project_folders()

        if not project_folders:
            st.info("📂 No project folders found. Create a new project to get started!")
            if st.button("🚀 Create New Project"):
                st.session_state.show_project_manager = False
                st.session_state.show_project_generation = True
                st.rerun()
            return

        st.subheader(f"📊 Found {len(project_folders)} Projects")

        # Project grid
        for i, project in enumerate(project_folders):
            with st.container():
                # Project header
                col1, col2, col3, col4 = st.columns([3, 1, 1, 1])

                with col1:
                    st.markdown(f"### 📁 {project['name']}")
                    if project.get('project_type'):
                        st.caption(f"**Type:** {project['project_type']}")

                with col2:
                    st.metric("Files", project['file_count'])

                with col3:
                    size_mb = project['total_size'] / (1024 * 1024)
                    st.metric("Size", f"{size_mb:.1f}MB")

                with col4:
                    modified_time = time.strftime('%Y-%m-%d', time.localtime(project['last_modified']))
                    st.caption(f"Modified: {modified_time}")

                # Project actions
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    if st.button("👁️ View Details", key=f"view_{i}"):
                        show_project_details(project['name'])

                with col2:
                    if st.button("📝 Edit Project", key=f"edit_{i}"):
                        select_project_for_editing(project['name'])

                with col3:
                    if st.button("💻 Open Terminal", key=f"terminal_{i}"):
                        open_project_terminal(project['name'])

                with col4:
                    if st.button("🗑️ Delete", key=f"delete_{i}", type="secondary"):
                        show_delete_confirmation(project['name'])

                st.divider()

    except Exception as e:
        st.error(f"Error loading projects: {e}")

def show_project_details(project_name: str):
    """Show detailed information about a project."""
    try:
        engine = st.session_state.analysis_engine
        project_info = engine.get_project_folder_info(project_name)

        if not project_info:
            st.error(f"Project '{project_name}' not found")
            return

        st.subheader(f"📋 Project Details: {project_name}")

        # Project metrics
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Files", project_info['total_files'])
        with col2:
            st.metric("Directories", project_info['total_directories'])
        with col3:
            st.metric("Code Files", project_info['code_files'])
        with col4:
            size_mb = project_info['total_size'] / (1024 * 1024)
            st.metric("Total Size", f"{size_mb:.1f}MB")

        # File breakdown
        st.subheader("📊 File Breakdown")

        files_by_type = project_info['files_by_type']

        # Code files
        if files_by_type['code']:
            with st.expander(f"💻 Code Files ({len(files_by_type['code'])})", expanded=True):
                for file_info in files_by_type['code']:
                    col1, col2 = st.columns([3, 1])
                    with col1:
                        st.write(f"📄 {file_info['name']}")
                    with col2:
                        st.caption(f"{file_info['size']} bytes")

        # Config files
        if files_by_type['config']:
            with st.expander(f"⚙️ Configuration Files ({len(files_by_type['config'])})"):
                for file_info in files_by_type['config']:
                    col1, col2 = st.columns([3, 1])
                    with col1:
                        st.write(f"📄 {file_info['name']}")
                    with col2:
                        st.caption(f"{file_info['size']} bytes")

        # Documentation files
        if files_by_type['docs']:
            with st.expander(f"📚 Documentation ({len(files_by_type['docs'])})"):
                for file_info in files_by_type['docs']:
                    col1, col2 = st.columns([3, 1])
                    with col1:
                        st.write(f"📄 {file_info['name']}")
                    with col2:
                        st.caption(f"{file_info['size']} bytes")

        # Other files
        if files_by_type['other']:
            with st.expander(f"📁 Other Files ({len(files_by_type['other'])})"):
                for file_info in files_by_type['other']:
                    col1, col2 = st.columns([3, 1])
                    with col1:
                        st.write(f"📄 {file_info['name']}")
                    with col2:
                        st.caption(f"{file_info['size']} bytes")

    except Exception as e:
        st.error(f"Error loading project details: {e}")

def select_project_for_editing(project_name: str):
    """Select a project for editing and switch to file analysis."""
    try:
        # Set the selected project in session state
        st.session_state.selected_project = project_name
        st.session_state.show_project_manager = False

        # Show success message
        st.success(f"✅ Selected project: {project_name}")
        st.info("You can now select files from this project in the sidebar file browser.")

        st.rerun()

    except Exception as e:
        st.error(f"Error selecting project: {e}")

def open_project_terminal(project_name: str):
    """Open terminal in the project directory."""
    try:
        # Set the project directory for terminal
        st.session_state.terminal_project_dir = project_name
        st.session_state.show_project_manager = False
        st.session_state.show_terminal = True

        st.success(f"✅ Opening terminal in project: {project_name}")
        st.rerun()

    except Exception as e:
        st.error(f"Error opening terminal: {e}")

def show_delete_confirmation(project_name: str):
    """Show delete confirmation dialog."""
    st.warning(f"⚠️ Are you sure you want to delete project '{project_name}'?")
    st.error("This action cannot be undone. All files in the project folder will be permanently deleted.")

    col1, col2, col3 = st.columns([1, 1, 2])

    with col1:
        if st.button("✅ Yes, Delete", key=f"confirm_delete_{project_name}", type="primary"):
            delete_project_folder(project_name)

    with col2:
        if st.button("❌ Cancel", key=f"cancel_delete_{project_name}"):
            st.rerun()

def delete_project_folder(project_name: str):
    """Delete a project folder."""
    try:
        engine = st.session_state.analysis_engine

        with st.spinner(f"🗑️ Deleting project '{project_name}'..."):
            success = engine.delete_project_folder(project_name)

        if success:
            st.success(f"✅ Project '{project_name}' deleted successfully!")
            st.balloons()

            # Clear any references to the deleted project
            if st.session_state.get('selected_project') == project_name:
                del st.session_state.selected_project

            time.sleep(1)  # Brief pause to show success message
            st.rerun()
        else:
            st.error(f"❌ Failed to delete project '{project_name}'")

    except Exception as e:
        st.error(f"❌ Error deleting project: {e}")

if __name__ == "__main__":
    main()
