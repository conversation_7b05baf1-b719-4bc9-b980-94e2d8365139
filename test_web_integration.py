#!/usr/bin/env python3
"""Test script to verify web interface project generation integration."""

from core.analysis_engine import AnalysisEngine
from core.ai_integration import AnalysisType

def test_project_generation():
    """Test project generation functionality."""
    print("🧪 Testing project generation integration...")
    
    try:
        # Initialize engine
        engine = AnalysisEngine()
        print("✅ AnalysisEngine initialized")
        
        # Test project generation
        session_id = engine.generate_project(
            prompt="Create a simple Flask web app with basic routing",
            project_name="test_flask_app",
            include_tests=True,
            include_docs=True
        )
        print(f"✅ Project generation successful! Session ID: {session_id}")
        
        # Get preview
        preview = engine.get_project_structure_preview(session_id)
        if preview:
            project_name = preview.get("project_name", "Unknown")
            total_files = preview.get("total_files", 0)
            summary = preview.get("summary", "No summary")
            
            print(f"📊 Project: {project_name}")
            print(f"📁 Total files: {total_files}")
            print(f"📋 Summary: {summary}")
            
            # Show directory structure
            directories = preview.get("directories", {})
            for dir_name, files in directories.items():
                print(f"📁 {dir_name}/")
                for file_info in files:
                    print(f"  📄 {file_info['name']} ({file_info['size']} bytes)")
            
            print("✅ Preview generation successful!")
            return True
        else:
            print("❌ Preview generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_project_generation()
    if success:
        print("\n🎉 All tests passed! Web interface integration is working.")
    else:
        print("\n💥 Tests failed! Check the error messages above.")
