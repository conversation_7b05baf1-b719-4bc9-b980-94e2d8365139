"""Tests for the FileManager class."""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock

from core.file_manager import FileManager, FileInfo
from config import Config

class TestFileManager:
    """Test cases for FileManager."""
    
    def setup_method(self):
        """Set up test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.file_manager = FileManager(self.temp_dir)
        
        # Create test files
        (self.temp_dir / "test.py").write_text("print('hello world')")
        (self.temp_dir / "test.js").write_text("console.log('hello world');")
        (self.temp_dir / "subdir").mkdir()
        (self.temp_dir / "subdir" / "nested.txt").write_text("nested content")
        
    def teardown_method(self):
        """Clean up test environment."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_list_files(self):
        """Test file listing functionality."""
        files = self.file_manager.list_files()
        
        # Should find the files we created
        file_names = [f.name for f in files]
        assert "test.py" in file_names
        assert "test.js" in file_names
        assert "subdir" in file_names
        
        # Test recursive listing
        files_recursive = self.file_manager.list_files(recursive=True)
        file_paths = [str(f.path) for f in files_recursive]
        assert any("nested.txt" in path for path in file_paths)
    
    def test_read_file(self):
        """Test file reading functionality."""
        content = self.file_manager.read_file("test.py")
        assert content == "print('hello world')"
        
        # Test reading non-existent file
        with pytest.raises(FileNotFoundError):
            self.file_manager.read_file("nonexistent.py")
    
    def test_write_file(self):
        """Test file writing functionality."""
        test_content = "# This is a test file\nprint('test')"
        
        success = self.file_manager.write_file("new_test.py", test_content)
        assert success
        
        # Verify file was written correctly
        written_content = self.file_manager.read_file("new_test.py")
        assert written_content == test_content
    
    def test_get_file_info(self):
        """Test file info retrieval."""
        file_info = self.file_manager.get_file_info("test.py")
        
        assert isinstance(file_info, FileInfo)
        assert file_info.name == "test.py"
        assert file_info.extension == ".py"
        assert file_info.is_text
        assert not file_info.is_directory
        assert file_info.size > 0
    
    def test_create_directory(self):
        """Test directory creation."""
        success = self.file_manager.create_directory("new_dir")
        assert success
        assert (self.temp_dir / "new_dir").exists()
        assert (self.temp_dir / "new_dir").is_dir()
    
    def test_delete_file(self):
        """Test file deletion."""
        # Create a file to delete
        test_file = "to_delete.txt"
        self.file_manager.write_file(test_file, "delete me")
        
        # Verify file exists
        assert (self.temp_dir / test_file).exists()
        
        # Delete file
        success = self.file_manager.delete_file(test_file)
        assert success
        assert not (self.temp_dir / test_file).exists()
    
    def test_search_files(self):
        """Test file search functionality."""
        # Create files with searchable content
        self.file_manager.write_file("search_test1.py", "def hello():\n    print('hello world')")
        self.file_manager.write_file("search_test2.py", "def goodbye():\n    print('goodbye world')")
        
        # Search for 'hello'
        results = self.file_manager.search_files("hello")
        
        assert len(results) >= 1
        file_info, matches = results[0]
        assert "hello" in matches[0][1].lower()
    
    def test_path_validation(self):
        """Test path validation and security."""
        # Test path traversal attempt
        with pytest.raises(PermissionError):
            self.file_manager.read_file("../../../etc/passwd")
        
        # Test absolute path outside workspace
        with pytest.raises(PermissionError):
            self.file_manager.read_file("/etc/passwd")
    
    @patch.object(Config, 'MAX_FILE_SIZE_BYTES', 10)
    def test_file_size_limit(self):
        """Test file size limitations."""
        large_content = "x" * 20  # Larger than our mocked limit
        
        # Writing should work
        self.file_manager.write_file("large_file.txt", large_content)
        
        # Reading should fail due to size limit
        with pytest.raises(ValueError, match="File too large"):
            self.file_manager.read_file("large_file.txt")
    
    @patch.object(Config, 'ALLOWED_EXTENSIONS', {'.py', '.txt'})
    @patch.object(Config, 'ENABLE_FILE_RESTRICTIONS', True)
    def test_extension_restrictions(self):
        """Test file extension restrictions."""
        # Allowed extension should work
        self.file_manager.write_file("allowed.py", "print('allowed')")
        content = self.file_manager.read_file("allowed.py")
        assert content == "print('allowed')"
        
        # Disallowed extension should fail
        with pytest.raises(PermissionError):
            self.file_manager.write_file("disallowed.exe", "binary content")
    
    def test_is_text_file(self):
        """Test text file detection."""
        # Create various file types
        self.file_manager.write_file("text.py", "print('hello')")
        self.file_manager.write_file("data.json", '{"key": "value"}')
        
        # Test text file detection
        py_info = self.file_manager.get_file_info("text.py")
        assert py_info.is_text
        
        json_info = self.file_manager.get_file_info("data.json")
        assert json_info.is_text
    
    def test_workspace_isolation(self):
        """Test that operations are isolated to workspace."""
        # All operations should be relative to workspace
        files = self.file_manager.list_files()
        for file_info in files:
            assert self.temp_dir in file_info.path.resolve().parents or file_info.path.resolve() == self.temp_dir
    
    def test_error_handling(self):
        """Test error handling in various scenarios."""
        # Test reading directory as file
        with pytest.raises(ValueError):
            self.file_manager.read_file("subdir")
        
        # Test getting info for non-existent file
        with pytest.raises(FileNotFoundError):
            self.file_manager.get_file_info("nonexistent.txt")
        
        # Test deleting non-existent file
        with pytest.raises(FileNotFoundError):
            self.file_manager.delete_file("nonexistent.txt")

if __name__ == "__main__":
    pytest.main([__file__])
