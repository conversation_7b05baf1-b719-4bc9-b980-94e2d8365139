#!/usr/bin/env python3
"""Test script to check config and allowed extensions."""

from config import Config

def test_config():
    """Test configuration and allowed extensions."""
    print("🧪 Testing configuration...")
    
    try:
        print(f"✅ Config loaded")
        print(f"📋 Default AI Provider: {Config.DEFAULT_AI_PROVIDER}")
        print(f"🔑 OpenAI API Key: {'✅ Set' if Config.OPENAI_API_KEY else '❌ Not set'}")
        print(f"🔑 Gemini API Key: {'✅ Set' if Config.GEMINI_API_KEY else '❌ Not set'}")
        
        print(f"\n📄 Allowed Extensions:")
        extensions = sorted(list(Config.ALLOWED_EXTENSIONS))
        for ext in extensions:
            print(f"  - {ext}")
        
        # Test specific extensions
        test_extensions = ['.jsx', '.tsx', '.js', '.ts', '.py', '.html', '.css']
        print(f"\n🧪 Testing specific extensions:")
        
        for ext in test_extensions:
            from pathlib import Path
            test_path = Path(f"test{ext}")
            allowed = Config.is_extension_allowed(test_path)
            status = "✅" if allowed else "❌"
            print(f"  {status} {ext}: {'Allowed' if allowed else 'Not allowed'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_config()
