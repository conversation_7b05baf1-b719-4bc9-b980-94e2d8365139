#!/usr/bin/env python3
"""Test creating a project with 'todo' folder name."""

from core.analysis_engine import AnalysisEng<PERSON>

def test_todo_folder():
    """Test creating a project in 'todo' folder."""
    print("🧪 Testing 'todo' folder creation...")
    
    try:
        engine = AnalysisEngine()
        
        # Test creating project with 'todo' folder
        print("🚀 Creating project in 'todo' folder...")
        session_id = engine.generate_project(
            prompt="Create a simple todo list app with HTML, CSS, and JavaScript",
            project_name="Todo List App",
            target_folder="todo"
        )
        
        print(f"✅ Project generated with session ID: {session_id}")
        
        # Check the file paths
        session = engine.get_session(session_id)
        if session and session.change_proposals:
            print(f"\n📁 Generated files:")
            for i, proposal in enumerate(session.change_proposals):
                file_path = proposal.file_diff.file_path
                print(f"  {i+1}. {file_path}")
                
                # Check if it starts with 'todo/'
                if file_path.startswith('todo/'):
                    print(f"     ✅ Correct folder: {file_path}")
                else:
                    print(f"     ❌ Wrong folder: {file_path}")
        
        # Apply the project
        print(f"\n📁 Applying project generation...")
        success = engine.apply_project_generation(session_id)
        print(f"✅ Apply result: {success}")
        
        # Check if 'todo' folder was created
        import os
        workspace_path = engine.file_manager.workspace_root
        todo_path = workspace_path / "todo"
        
        if todo_path.exists() and todo_path.is_dir():
            print(f"✅ 'todo' folder created successfully at: {todo_path}")
            
            # List files in todo folder
            todo_files = list(todo_path.glob("*"))
            print(f"📄 Files in 'todo' folder:")
            for file_path in todo_files:
                if file_path.is_file():
                    print(f"  - {file_path.name} ({file_path.stat().st_size} bytes)")
        else:
            print(f"❌ 'todo' folder not found at: {todo_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_todo_folder()
    if success:
        print("\n🎉 'todo' folder creation test passed!")
    else:
        print("\n💥 'todo' folder creation test failed!")
