"""Tests for the DiffManager class."""

import pytest
import time
from unittest.mock import MagicMock

from core.diff_manager import <PERSON>ff<PERSON>ana<PERSON>, ChangeStatus, ChangeProposal, FileDiff

class TestDiffManager:
    """Test cases for DiffManager."""
    
    def setup_method(self):
        """Set up test environment."""
        self.diff_manager = DiffManager()
        
        # Sample content for testing
        self.old_content = """def hello():
    print("Hello, World!")
    return True

def goodbye():
    print("Goodbye!")"""

        self.new_content = """def hello():
    print("Hello, Beautiful World!")
    return True

def goodbye():
    print("Goodbye, my friend!")
    
def new_function():
    return "new feature\""""
    
    def test_create_diff(self):
        """Test diff creation."""
        file_diff = self.diff_manager.create_diff(
            self.old_content, 
            self.new_content, 
            "test.py"
        )
        
        assert isinstance(file_diff, FileDiff)
        assert file_diff.file_path == "test.py"
        assert file_diff.old_content == self.old_content
        assert file_diff.new_content == self.new_content
        assert len(file_diff.hunks) > 0
        assert file_diff.stats['additions'] > 0
        assert file_diff.stats['deletions'] > 0
    
    def test_diff_statistics(self):
        """Test diff statistics calculation."""
        file_diff = self.diff_manager.create_diff(
            self.old_content, 
            self.new_content, 
            "test.py"
        )
        
        stats = file_diff.stats
        assert 'additions' in stats
        assert 'deletions' in stats
        assert 'changes' in stats
        assert stats['changes'] == stats['additions'] + stats['deletions']
    
    def test_create_change_proposal(self):
        """Test change proposal creation."""
        proposal = self.diff_manager.create_change_proposal(
            title="Improve greeting messages",
            description="Make greetings more friendly",
            old_content=self.old_content,
            new_content=self.new_content,
            file_path="test.py",
            confidence=0.8,
            reasoning="Better user experience",
            suggestion_type="improvement"
        )
        
        assert isinstance(proposal, ChangeProposal)
        assert proposal.title == "Improve greeting messages"
        assert proposal.status == ChangeStatus.PENDING
        assert proposal.confidence == 0.8
        assert proposal.id in self.diff_manager.pending_changes
    
    def test_accept_change(self):
        """Test accepting a change proposal."""
        proposal = self.diff_manager.create_change_proposal(
            title="Test change",
            description="Test description",
            old_content=self.old_content,
            new_content=self.new_content,
            file_path="test.py"
        )
        
        # Accept the change
        success = self.diff_manager.accept_change(proposal.id)
        assert success
        assert proposal.status == ChangeStatus.ACCEPTED
    
    def test_reject_change(self):
        """Test rejecting a change proposal."""
        proposal = self.diff_manager.create_change_proposal(
            title="Test change",
            description="Test description",
            old_content=self.old_content,
            new_content=self.new_content,
            file_path="test.py"
        )
        
        # Reject the change
        success = self.diff_manager.reject_change(proposal.id, "Not needed")
        assert success
        assert proposal.status == ChangeStatus.REJECTED
        assert proposal.id not in self.diff_manager.pending_changes
        assert proposal in self.diff_manager.change_history
    
    def test_apply_change(self):
        """Test applying an accepted change."""
        # Create mock file manager
        mock_file_manager = MagicMock()
        mock_file_manager.write_file.return_value = True
        
        proposal = self.diff_manager.create_change_proposal(
            title="Test change",
            description="Test description",
            old_content=self.old_content,
            new_content=self.new_content,
            file_path="test.py"
        )
        
        # Accept and apply the change
        self.diff_manager.accept_change(proposal.id)
        success = self.diff_manager.apply_change(proposal.id, mock_file_manager)
        
        assert success
        assert proposal.status == ChangeStatus.APPLIED
        assert proposal.id not in self.diff_manager.pending_changes
        assert proposal in self.diff_manager.change_history
        
        # Verify file manager was called
        mock_file_manager.write_file.assert_called_once_with(
            "test.py", 
            self.new_content
        )
    
    def test_get_pending_changes(self):
        """Test retrieving pending changes."""
        # Create multiple proposals
        proposal1 = self.diff_manager.create_change_proposal(
            title="Change 1", description="Desc 1",
            old_content=self.old_content, new_content=self.new_content,
            file_path="test1.py"
        )
        
        proposal2 = self.diff_manager.create_change_proposal(
            title="Change 2", description="Desc 2",
            old_content=self.old_content, new_content=self.new_content,
            file_path="test2.py"
        )
        
        pending = self.diff_manager.get_pending_changes()
        assert len(pending) == 2
        assert proposal1 in pending
        assert proposal2 in pending
    
    def test_format_diff_for_display(self):
        """Test diff formatting for display."""
        file_diff = self.diff_manager.create_diff(
            self.old_content, 
            self.new_content, 
            "test.py"
        )
        
        formatted = self.diff_manager.format_diff_for_display(file_diff)
        
        assert isinstance(formatted, str)
        assert "test.py" in formatted
        assert "+" in formatted  # Should show additions
        assert "-" in formatted  # Should show deletions
    
    def test_change_history(self):
        """Test change history tracking."""
        proposal = self.diff_manager.create_change_proposal(
            title="Test change",
            description="Test description",
            old_content=self.old_content,
            new_content=self.new_content,
            file_path="test.py"
        )
        
        # Initially no history
        history = self.diff_manager.get_change_history()
        assert len(history) == 0
        
        # Reject change - should move to history
        self.diff_manager.reject_change(proposal.id)
        history = self.diff_manager.get_change_history()
        assert len(history) == 1
        assert proposal in history
    
    def test_export_changes(self):
        """Test exporting changes to file."""
        import tempfile
        import json
        import os
        
        proposal = self.diff_manager.create_change_proposal(
            title="Test change",
            description="Test description",
            old_content=self.old_content,
            new_content=self.new_content,
            file_path="test.py"
        )
        
        # Export to temporary file
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
            export_path = f.name
        
        try:
            success = self.diff_manager.export_changes(export_path)
            assert success
            
            # Verify export file
            with open(export_path, 'r') as f:
                data = json.load(f)
            
            assert 'pending_changes' in data
            assert 'exported_at' in data
            assert len(data['pending_changes']) == 1
            
            exported_change = data['pending_changes'][0]
            assert exported_change['title'] == "Test change"
            assert exported_change['file_path'] == "test.py"
            
        finally:
            os.unlink(export_path)
    
    def test_clear_pending_changes(self):
        """Test clearing all pending changes."""
        # Create multiple proposals
        for i in range(3):
            self.diff_manager.create_change_proposal(
                title=f"Change {i}",
                description=f"Description {i}",
                old_content=self.old_content,
                new_content=self.new_content,
                file_path=f"test{i}.py"
            )
        
        assert len(self.diff_manager.pending_changes) == 3
        
        # Clear all changes
        count = self.diff_manager.clear_pending_changes()
        assert count == 3
        assert len(self.diff_manager.pending_changes) == 0
    
    def test_error_handling(self):
        """Test error handling in various scenarios."""
        # Test accepting non-existent change
        with pytest.raises(ValueError):
            self.diff_manager.accept_change("nonexistent_id")
        
        # Test rejecting non-existent change
        with pytest.raises(ValueError):
            self.diff_manager.reject_change("nonexistent_id")
        
        # Test applying non-existent change
        mock_file_manager = MagicMock()
        with pytest.raises(ValueError):
            self.diff_manager.apply_change("nonexistent_id", mock_file_manager)
        
        # Test applying non-accepted change
        proposal = self.diff_manager.create_change_proposal(
            title="Test change",
            description="Test description",
            old_content=self.old_content,
            new_content=self.new_content,
            file_path="test.py"
        )
        
        with pytest.raises(ValueError):
            self.diff_manager.apply_change(proposal.id, mock_file_manager)
    
    def test_identical_content(self):
        """Test diff creation with identical content."""
        file_diff = self.diff_manager.create_diff(
            self.old_content, 
            self.old_content,  # Same content
            "test.py"
        )
        
        assert file_diff.stats['additions'] == 0
        assert file_diff.stats['deletions'] == 0
        assert file_diff.stats['changes'] == 0

if __name__ == "__main__":
    pytest.main([__file__])
