#!/usr/bin/env python3
"""Final integration test for all features."""

from core.analysis_engine import AnalysisEngine

def test_final_integration():
    """Test all features working together."""
    print("🧪 Final Integration Test")
    print("=" * 50)
    
    try:
        # Initialize engine
        engine = AnalysisEngine()
        print("✅ AnalysisEngine initialized")
        
        # Test AI provider switching
        print(f"\n🧠 Testing AI Provider Management")
        available_providers = engine.ai_manager.get_available_providers()
        print(f"📋 Available providers: {available_providers}")
        print(f"🎯 Current provider: {engine.ai_manager.default_provider}")
        
        # Test project generation with OpenAI
        print(f"\n🚀 Testing Project Generation with OpenAI")
        engine.ai_manager.switch_default_provider("openai")
        
        session_id = engine.generate_project(
            prompt="Create a simple React todo app with modern features",
            project_name="Final Test React",
            preferred_framework="React",
            target_folder="final_test_react"
        )
        
        print(f"✅ OpenAI project generated: {session_id}")
        
        # Apply the project
        success = engine.apply_project_generation(session_id)
        print(f"📁 OpenAI project applied: {success}")
        
        # Check files created
        workspace_root = engine.file_manager.workspace_root
        react_folder = workspace_root / "final_test_react"
        
        if react_folder.exists():
            files = list(react_folder.rglob("*"))
            file_count = len([f for f in files if f.is_file()])
            print(f"✅ OpenAI created {file_count} files in final_test_react")
            
            # Show some files
            for file_path in files[:5]:
                if file_path.is_file():
                    rel_path = file_path.relative_to(react_folder)
                    print(f"  - {rel_path}")
        
        # Test project generation with Gemini
        print(f"\n⚡ Testing Project Generation with Gemini")
        engine.ai_manager.switch_default_provider("gemini")
        
        session_id2 = engine.generate_project(
            prompt="Create a simple Python CLI calculator",
            project_name="Final Test Python",
            preferred_framework="Python",
            target_folder="final_test_python"
        )
        
        print(f"✅ Gemini project generated: {session_id2}")
        
        # Apply the project
        success2 = engine.apply_project_generation(session_id2)
        print(f"📁 Gemini project applied: {success2}")
        
        # Check files created
        python_folder = workspace_root / "final_test_python"
        
        if python_folder.exists():
            files = list(python_folder.rglob("*"))
            file_count = len([f for f in files if f.is_file()])
            print(f"✅ Gemini created {file_count} files in final_test_python")
            
            # Show some files
            for file_path in files[:5]:
                if file_path.is_file():
                    rel_path = file_path.relative_to(python_folder)
                    print(f"  - {rel_path}")
        
        # Test project management
        print(f"\n📁 Testing Project Management")
        project_folders = engine.list_project_folders()
        print(f"✅ Found {len(project_folders)} project folders")
        
        for project in project_folders[-2:]:  # Show last 2 projects
            print(f"  📁 {project['name']}: {project['file_count']} files, {project['project_type']}")
        
        # Test code analysis
        print(f"\n🔍 Testing Code Analysis")
        
        # Create a test file
        test_file = python_folder / "test_analysis.py"
        test_code = '''
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# Inefficient recursive implementation
for i in range(10):
    print(f"fib({i}) = {fibonacci(i)}")
'''
        
        with open(test_file, 'w') as f:
            f.write(test_code)
        
        # Analyze with current provider (Gemini)
        from core.ai_integration import AnalysisType
        analysis_result = engine.analyze_file(
            file_path=str(test_file.relative_to(workspace_root)),
            analysis_type=AnalysisType.OPTIMIZE
        )
        
        print(f"✅ Code analysis completed")
        print(f"📝 Summary: {analysis_result.ai_result.summary[:100]}...")
        print(f"💡 Suggestions: {len(analysis_result.ai_result.suggestions)}")
        
        # Test provider switching back
        print(f"\n🔄 Testing Provider Switching")
        engine.ai_manager.switch_default_provider("openai")
        print(f"✅ Switched back to: {engine.ai_manager.default_provider}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_interface_features():
    """Test web interface specific features."""
    print(f"\n🌐 Testing Web Interface Features")
    
    try:
        # Test that all components can be imported
        from web_interface import main
        print("✅ Web interface imports successful")
        
        # Test AI manager integration
        engine = AnalysisEngine()
        ai_manager = engine.ai_manager
        
        # Test provider info
        providers = ai_manager.get_available_providers()
        print(f"✅ Web interface can access {len(providers)} providers")
        
        # Test best provider selection
        best_for_code = ai_manager.get_best_provider("code_generation")
        best_for_analysis = ai_manager.get_best_provider("simple_analysis")
        
        print(f"🎯 Best for code generation: {best_for_code.value}")
        print(f"⚡ Best for simple analysis: {best_for_analysis.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Web interface test error: {e}")
        return False

if __name__ == "__main__":
    # Test core functionality
    core_success = test_final_integration()
    
    # Test web interface
    web_success = test_web_interface_features()
    
    print(f"\n🎉 Final Integration Test Results:")
    print(f"✅ Core Functionality: {'PASS' if core_success else 'FAIL'}")
    print(f"✅ Web Interface: {'PASS' if web_success else 'FAIL'}")
    
    if core_success and web_success:
        print(f"\n🚀 ALL TESTS PASSED!")
        print(f"✅ OpenAI ChatGPT integration working")
        print(f"✅ Gemini integration working") 
        print(f"✅ AI provider switching working")
        print(f"✅ Project generation working")
        print(f"✅ File creation working")
        print(f"✅ Project management working")
        print(f"✅ Code analysis working")
        print(f"✅ Web interface ready")
        print(f"\n🌐 Web interface available at: http://localhost:8501")
        print(f"📋 Features available:")
        print(f"  - 🤖 AI Provider Selection (OpenAI/Gemini/Auto)")
        print(f"  - 🚀 Project Generation with custom folders")
        print(f"  - ✅ Accept/Reject file creation")
        print(f"  - 📁 Project Manager")
        print(f"  - 💻 Terminal access")
        print(f"  - 🔍 Code analysis")
    else:
        print(f"\n💥 Some tests failed - check the errors above")
