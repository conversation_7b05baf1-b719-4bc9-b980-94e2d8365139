"""Main entry point for the AI Code Assistant."""

import sys
import argparse
from pathlib import Path
from config import Config
from rich.console import Console
from rich.panel import Panel

console = Console()

def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(description="AI Code Assistant with Gemini Integration")
    parser.add_argument("--mode", choices=["cli", "web"], default="web", 
                       help="Run in CLI or web interface mode")
    parser.add_argument("--workspace", type=str, help="Override workspace directory")
    parser.add_argument("--port", type=int, default=8501, help="Port for web interface")
    
    args = parser.parse_args()
    
    # Override workspace if provided
    if args.workspace:
        Config.WORKSPACE_ROOT = Path(args.workspace)
    
    # Validate configuration
    errors = Config.validate()
    if errors:
        console.print(Panel(
            "\n".join(f"❌ {error}" for error in errors),
            title="Configuration Errors",
            border_style="red"
        ))
        sys.exit(1)
    
    console.print(Panel(
        f"🚀 AI Code Assistant Starting\n"
        f"Mode: {args.mode}\n"
        f"Workspace: {Config.WORKSPACE_ROOT}\n"
        f"API Key: {'✅ Configured' if Config.GEMINI_API_KEY else '❌ Missing'}",
        title="AI Code Assistant",
        border_style="green"
    ))
    
    if args.mode == "web":
        run_web_interface(args.port)
    else:
        run_cli_interface()

def run_web_interface(port: int):
    """Run the Streamlit web interface."""
    import subprocess
    import os
    
    # Set environment variables for Streamlit
    env = os.environ.copy()
    env["STREAMLIT_SERVER_PORT"] = str(port)
    env["STREAMLIT_SERVER_HEADLESS"] = "true"
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "web_interface.py",
            "--server.port", str(port),
            "--server.headless", "true"
        ], env=env)
    except KeyboardInterrupt:
        console.print("\n👋 Goodbye!")
    except Exception as e:
        console.print(f"❌ Error starting web interface: {e}")

def run_cli_interface():
    """Run the CLI interface."""
    from cli_interface import CLIInterface
    
    try:
        cli = CLIInterface()
        cli.run()
    except KeyboardInterrupt:
        console.print("\n👋 Goodbye!")
    except Exception as e:
        console.print(f"❌ Error in CLI interface: {e}")

if __name__ == "__main__":
    main()
