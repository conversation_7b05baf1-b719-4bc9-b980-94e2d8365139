#!/usr/bin/env python3
"""Debug web interface button issues."""

import streamlit as st
from core.analysis_engine import AnalysisEngine

def debug_streamlit_buttons():
    """Debug why Streamlit buttons aren't working."""
    print("🔍 Debugging Streamlit Button Issues")
    print("=" * 50)
    
    # The issue might be:
    # 1. Button event not triggering
    # 2. Function not being called
    # 3. Session state issues
    # 4. Streamlit rerun issues
    
    print("🧪 Testing button event simulation...")
    
    try:
        # Initialize engine like web interface does
        engine = AnalysisEngine()
        print("✅ AnalysisEngine initialized")
        
        # Switch to Gemini (free)
        engine.ai_manager.switch_default_provider("gemini")
        print("⚡ Switched to Gemini")
        
        # Create a simple change proposal (like web interface would have)
        print("\n📝 Creating test change proposal...")
        
        proposal = engine.diff_manager.create_change_proposal(
            title="Test File",
            description="Test file for button debugging",
            old_content="",
            new_content="print('Hello from test!')\n",
            file_path="test_button.py",
            confidence=0.9,
            reasoning="Test proposal",
            suggestion_type="file_creation"
        )
        
        print(f"✅ Created proposal: {proposal.id}")
        print(f"📄 File path: {proposal.file_diff.file_path}")
        print(f"🔄 Status: {proposal.status}")
        
        # Test the exact functions that web interface calls
        print(f"\n🔧 Testing web interface functions...")
        
        # Simulate accept_change function (from web interface)
        def test_accept_change(change_id: str):
            """Test the accept_change function like web interface."""
            try:
                print(f"🔄 Calling accept_change({change_id})")
                engine.diff_manager.accept_change(change_id)
                print(f"✅ Accept successful")
                return True
            except Exception as e:
                print(f"❌ Accept failed: {e}")
                return False
        
        # Simulate apply_change function (from web interface)
        def test_apply_change(change_id: str):
            """Test the apply_change function like web interface."""
            try:
                print(f"🔄 Calling apply_change({change_id})")
                engine.diff_manager.apply_change(change_id, engine.file_manager)
                print(f"✅ Apply successful")
                return True
            except Exception as e:
                print(f"❌ Apply failed: {e}")
                return False
        
        # Test accept workflow
        print(f"\n1️⃣ Testing Accept Workflow:")
        accept_success = test_accept_change(proposal.id)
        
        if accept_success:
            # Check status
            updated_proposal = engine.diff_manager.get_change_proposal(proposal.id)
            if updated_proposal:
                print(f"   Status after accept: {updated_proposal.status}")
            
            # Test apply workflow
            print(f"\n2️⃣ Testing Apply Workflow:")
            apply_success = test_apply_change(proposal.id)
            
            if apply_success:
                # Check if file was created
                workspace_root = engine.file_manager.workspace_root
                test_file = workspace_root / "test_button.py"
                
                if test_file.exists():
                    print(f"✅ File created: {test_file}")
                    print(f"   Size: {test_file.stat().st_size} bytes")
                    
                    # Read content
                    with open(test_file, 'r') as f:
                        content = f.read()
                    print(f"   Content: {repr(content)}")
                else:
                    print(f"❌ File not created: {test_file}")
                    return False
        
        # Test reject workflow
        print(f"\n3️⃣ Testing Reject Workflow:")
        
        # Create another proposal for reject test
        reject_proposal = engine.diff_manager.create_change_proposal(
            title="Reject Test",
            description="Test file for reject",
            old_content="",
            new_content="# This will be rejected\n",
            file_path="reject_test.py",
            confidence=0.8,
            reasoning="Test reject",
            suggestion_type="file_creation"
        )
        
        def test_reject_change(change_id: str):
            """Test the reject_change function like web interface."""
            try:
                print(f"🔄 Calling reject_change({change_id})")
                engine.diff_manager.reject_change(change_id, "User rejected")
                print(f"✅ Reject successful")
                return True
            except Exception as e:
                print(f"❌ Reject failed: {e}")
                return False
        
        reject_success = test_reject_change(reject_proposal.id)
        
        if reject_success:
            # Check if proposal is removed/rejected
            updated_proposal = engine.diff_manager.get_change_proposal(reject_proposal.id)
            if updated_proposal:
                print(f"   Status after reject: {updated_proposal.status}")
            else:
                print(f"   ✅ Proposal removed from pending (rejected)")
        
        print(f"\n🎉 All core functions working!")
        print(f"✅ accept_change: Working")
        print(f"✅ apply_change: Working")
        print(f"✅ reject_change: Working")
        print(f"✅ File creation: Working")
        
        print(f"\n🔍 The issue is likely in the Streamlit web interface:")
        print(f"   - Button events not triggering")
        print(f"   - Session state issues")
        print(f"   - Function not being called")
        print(f"   - Streamlit rerun problems")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_streamlit_buttons()
    
    if success:
        print(f"\n🚀 **CORE FUNCTIONS WORKING!**")
        print(f"✅ The problem is in the Streamlit web interface")
        print(f"✅ Need to fix button event handling")
        print(f"⚡ Using Gemini (free API)")
    else:
        print(f"\n💥 Core functions have issues")
