"""Google Gemini AI integration for code analysis and suggestions."""

import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import google.generativeai as genai
from config import Config

logger = logging.getLogger(__name__)

class AnalysisType(Enum):
    """Types of code analysis."""
    REVIEW = "review"
    REFACTOR = "refactor"
    OPTIMIZE = "optimize"
    DOCUMENT = "document"
    DEBUG = "debug"
    COMPLETE = "complete"
    EXPLAIN = "explain"

@dataclass
class CodeSuggestion:
    """A code suggestion from the AI."""
    type: AnalysisType
    title: str
    description: str
    original_code: str
    suggested_code: str
    confidence: float
    reasoning: str
    file_path: str
    line_start: Optional[int] = None
    line_end: Optional[int] = None

@dataclass
class AnalysisResult:
    """Result of AI code analysis."""
    suggestions: List[CodeSuggestion]
    summary: str
    analysis_time: float
    token_usage: Optional[Dict[str, int]] = None

class GeminiIntegration:
    """Integration with Google Gemini AI for code analysis."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize Gemini integration."""
        self.api_key = api_key or Config.GEMINI_API_KEY
        
        if not self.api_key:
            raise ValueError("Gemini API key is required")
        
        # Configure Gemini
        genai.configure(api_key=self.api_key)
        
        # Initialize model
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # Minimum seconds between requests
        
        logger.info("Gemini integration initialized successfully")
    
    def _rate_limit(self):
        """Implement basic rate limiting."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def analyze_code(self, code: str, file_path: str, 
                    analysis_type: AnalysisType = AnalysisType.REVIEW,
                    context: Optional[str] = None) -> AnalysisResult:
        """Analyze code and provide suggestions."""
        start_time = time.time()
        
        try:
            self._rate_limit()
            
            # Build prompt based on analysis type
            prompt = self._build_prompt(code, file_path, analysis_type, context)
            
            # Generate response
            response = self.model.generate_content(prompt)
            
            # Parse response
            suggestions = self._parse_response(response.text, code, file_path, analysis_type)
            
            analysis_time = time.time() - start_time
            
            # Extract summary
            summary = self._extract_summary(response.text)
            
            return AnalysisResult(
                suggestions=suggestions,
                summary=summary,
                analysis_time=analysis_time,
                token_usage=self._get_token_usage(response)
            )
            
        except Exception as e:
            logger.error(f"Error analyzing code: {e}")
            raise
    
    def _build_prompt(self, code: str, file_path: str, 
                     analysis_type: AnalysisType, context: Optional[str]) -> str:
        """Build appropriate prompt for the analysis type."""
        
        base_context = f"""
You are an expert code reviewer and assistant. Analyze the following code and provide specific, actionable suggestions.

File: {file_path}
Analysis Type: {analysis_type.value}
"""
        
        if context:
            base_context += f"\nAdditional Context: {context}"
        
        prompts = {
            AnalysisType.REVIEW: """
Please review this code for:
1. Code quality and best practices
2. Potential bugs or issues
3. Performance improvements
4. Security vulnerabilities
5. Readability and maintainability

Provide specific suggestions with exact code replacements where applicable.
""",
            AnalysisType.REFACTOR: """
Please suggest refactoring improvements for this code:
1. Extract methods/functions for better modularity
2. Improve naming conventions
3. Reduce code duplication
4. Simplify complex logic
5. Apply design patterns where appropriate

Provide the refactored code with explanations.
""",
            AnalysisType.OPTIMIZE: """
Please analyze this code for performance optimizations:
1. Algorithm efficiency improvements
2. Memory usage optimization
3. Database query optimization (if applicable)
4. Caching opportunities
5. Bottleneck identification

Provide optimized code with performance impact explanations.
""",
            AnalysisType.DOCUMENT: """
Please improve the documentation for this code:
1. Add comprehensive docstrings/comments
2. Explain complex logic
3. Document parameters and return values
4. Add usage examples
5. Include type hints where missing

Provide the fully documented version.
""",
            AnalysisType.DEBUG: """
Please help debug this code:
1. Identify potential runtime errors
2. Suggest debugging strategies
3. Add error handling where needed
4. Identify edge cases
5. Suggest logging/monitoring improvements

Provide fixes and debugging enhancements.
""",
            AnalysisType.COMPLETE: """
Please help complete this code:
1. Identify incomplete functions/methods
2. Suggest missing implementations
3. Add error handling
4. Complete TODO items
5. Ensure consistency with existing patterns

Provide the completed code.
""",
            AnalysisType.EXPLAIN: """
Please explain this code:
1. Overall purpose and functionality
2. Key algorithms and logic
3. Data structures used
4. Dependencies and relationships
5. Potential use cases

Provide a clear, educational explanation.
"""
        }
        
        specific_prompt = prompts.get(analysis_type, prompts[AnalysisType.REVIEW])
        
        full_prompt = f"""{base_context}

{specific_prompt}

Code to analyze:
```
{code}
```

Please format your response as follows:
SUMMARY: [Brief overview of findings]

SUGGESTIONS:
[For each suggestion, provide:]
- TITLE: [Brief title]
- DESCRIPTION: [Detailed explanation]
- CONFIDENCE: [High/Medium/Low]
- REASONING: [Why this suggestion is important]
- ORIGINAL: [Original code section]
- SUGGESTED: [Improved code section]
---

Ensure all code suggestions are syntactically correct and maintain the original functionality.
"""
        
        return full_prompt
    
    def _parse_response(self, response_text: str, original_code: str, 
                       file_path: str, analysis_type: AnalysisType) -> List[CodeSuggestion]:
        """Parse AI response into structured suggestions."""
        suggestions = []
        
        try:
            # Split response into sections
            sections = response_text.split("SUGGESTIONS:")
            if len(sections) < 2:
                return suggestions
            
            suggestions_text = sections[1]
            suggestion_blocks = suggestions_text.split("---")
            
            for block in suggestion_blocks:
                if not block.strip():
                    continue
                
                suggestion = self._parse_suggestion_block(
                    block.strip(), original_code, file_path, analysis_type
                )
                if suggestion:
                    suggestions.append(suggestion)
            
            return suggestions
            
        except Exception as e:
            logger.warning(f"Error parsing AI response: {e}")
            # Fallback: create a single suggestion with the full response
            return [CodeSuggestion(
                type=analysis_type,
                title="AI Analysis",
                description=response_text[:500] + "..." if len(response_text) > 500 else response_text,
                original_code=original_code,
                suggested_code=original_code,
                confidence=0.5,
                reasoning="Automated analysis",
                file_path=file_path
            )]
    
    def _parse_suggestion_block(self, block: str, original_code: str, 
                               file_path: str, analysis_type: AnalysisType) -> Optional[CodeSuggestion]:
        """Parse a single suggestion block."""
        try:
            lines = block.split('\n')
            
            title = ""
            description = ""
            confidence = 0.5
            reasoning = ""
            original = original_code
            suggested = original_code
            
            current_section = None
            current_content = []
            
            for line in lines:
                line = line.strip()
                
                if line.startswith("TITLE:"):
                    title = line[6:].strip()
                elif line.startswith("DESCRIPTION:"):
                    current_section = "description"
                    current_content = [line[12:].strip()]
                elif line.startswith("CONFIDENCE:"):
                    conf_text = line[11:].strip().lower()
                    confidence = {"high": 0.9, "medium": 0.6, "low": 0.3}.get(conf_text, 0.5)
                elif line.startswith("REASONING:"):
                    current_section = "reasoning"
                    current_content = [line[10:].strip()]
                elif line.startswith("ORIGINAL:"):
                    current_section = "original"
                    current_content = []
                elif line.startswith("SUGGESTED:"):
                    current_section = "suggested"
                    current_content = []
                elif current_section and line:
                    current_content.append(line)
                elif current_section:
                    # End of section
                    content = '\n'.join(current_content).strip()
                    if current_section == "description":
                        description = content
                    elif current_section == "reasoning":
                        reasoning = content
                    elif current_section == "original":
                        original = content.replace('```', '').strip()
                    elif current_section == "suggested":
                        suggested = content.replace('```', '').strip()
                    current_section = None
                    current_content = []
            
            # Handle last section
            if current_section and current_content:
                content = '\n'.join(current_content).strip()
                if current_section == "description":
                    description = content
                elif current_section == "reasoning":
                    reasoning = content
                elif current_section == "original":
                    original = content.replace('```', '').strip()
                elif current_section == "suggested":
                    suggested = content.replace('```', '').strip()
            
            if title and description:
                return CodeSuggestion(
                    type=analysis_type,
                    title=title,
                    description=description,
                    original_code=original,
                    suggested_code=suggested,
                    confidence=confidence,
                    reasoning=reasoning,
                    file_path=file_path
                )
            
            return None
            
        except Exception as e:
            logger.warning(f"Error parsing suggestion block: {e}")
            return None
    
    def _extract_summary(self, response_text: str) -> str:
        """Extract summary from AI response."""
        try:
            if "SUMMARY:" in response_text:
                summary_section = response_text.split("SUMMARY:")[1]
                summary = summary_section.split("SUGGESTIONS:")[0].strip()
                return summary
            return "AI analysis completed"
        except:
            return "AI analysis completed"
    
    def _get_token_usage(self, response) -> Optional[Dict[str, int]]:
        """Extract token usage information if available."""
        try:
            if hasattr(response, 'usage_metadata'):
                return {
                    'prompt_tokens': response.usage_metadata.prompt_token_count,
                    'completion_tokens': response.usage_metadata.candidates_token_count,
                    'total_tokens': response.usage_metadata.total_token_count
                }
        except:
            pass
        return None
    
    def quick_fix(self, code: str, error_message: str, file_path: str) -> Optional[str]:
        """Get a quick fix suggestion for a specific error."""
        try:
            self._rate_limit()
            
            prompt = f"""
Please provide a quick fix for this error in the code:

Error: {error_message}
File: {file_path}

Code:
```
{code}
```

Provide only the corrected code without explanations.
"""
            
            response = self.model.generate_content(prompt)
            fixed_code = response.text.strip()
            
            # Clean up response
            if fixed_code.startswith('```'):
                lines = fixed_code.split('\n')
                fixed_code = '\n'.join(lines[1:-1])
            
            return fixed_code
            
        except Exception as e:
            logger.error(f"Error getting quick fix: {e}")
            return None
