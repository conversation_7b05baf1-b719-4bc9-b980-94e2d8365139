#!/usr/bin/env python3
"""Debug script to test folder naming logic."""

from core.analysis_engine import Analysis<PERSON>ngine

def test_folder_naming():
    """Test folder naming with different inputs."""
    print("🔍 Testing folder naming logic...")
    
    try:
        engine = AnalysisEngine()
        
        # Test 1: Explicit folder name
        print("\n🧪 Test 1: Explicit folder name 'todo'")
        session_id = engine.generate_project(
            prompt="Create a simple todo app",
            project_name="My Todo App",
            target_folder="todo"
        )
        
        # Get the session to see what folder was actually used
        session = engine.get_session(session_id)
        if session and session.change_proposals:
            first_file_path = session.change_proposals[0].file_diff.file_path
            print(f"📁 First file path: {first_file_path}")
            
            # Extract folder from path
            if '/' in first_file_path:
                actual_folder = first_file_path.split('/')[0]
                print(f"📂 Actual folder created: '{actual_folder}'")
                print(f"✅ Expected: 'todo', Got: '{actual_folder}'")
                
                if actual_folder == "todo":
                    print("✅ Folder naming is correct!")
                else:
                    print("❌ Folder naming is incorrect!")
            else:
                print("❌ No folder in path - files created in root")
        
        # Test 2: Project name as folder
        print("\n🧪 Test 2: Using project name as folder")
        session_id2 = engine.generate_project(
            prompt="Create a simple calculator",
            project_name="calculator_app",
            target_folder=None  # Should use project name
        )
        
        session2 = engine.get_session(session_id2)
        if session2 and session2.change_proposals:
            first_file_path2 = session2.change_proposals[0].file_diff.file_path
            print(f"📁 First file path: {first_file_path2}")
            
            if '/' in first_file_path2:
                actual_folder2 = first_file_path2.split('/')[0]
                print(f"📂 Actual folder created: '{actual_folder2}'")
                print(f"✅ Expected: 'calculator_app', Got: '{actual_folder2}'")
        
        # Test 3: Auto-generated folder
        print("\n🧪 Test 3: Auto-generated folder name")
        session_id3 = engine.generate_project(
            prompt="Create a simple web scraper",
            project_name=None,
            target_folder=None
        )
        
        session3 = engine.get_session(session_id3)
        if session3 and session3.change_proposals:
            first_file_path3 = session3.change_proposals[0].file_diff.file_path
            print(f"📁 First file path: {first_file_path3}")
            
            if '/' in first_file_path3:
                actual_folder3 = first_file_path3.split('/')[0]
                print(f"📂 Actual folder created: '{actual_folder3}'")
                print(f"✅ Should start with 'generated_project_'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_folder_naming()
