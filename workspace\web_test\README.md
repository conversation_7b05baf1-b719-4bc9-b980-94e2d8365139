# Web Test

Project generated from: 
Analyze this project request and extract structured information:

Request: "Create a simple Python hello world script"

Please provide a JSON response with the following structure:
{
    "project_type": "web_app|api|desktop_app|data_science|mobile_app|cli_tool|library|game|automation|microservice",
    "framework": "react|angular|vue|flask|django|fastapi|express|nextjs|tkinter|pyqt|electron|jupyter|streamlit|dash|react_native|flutter|vanilla|custom",
    "language": "python|javascript|typescript|java|csharp|go|rust|php|ruby|swift|kotlin",
    "features": ["list", "of", "required", "features"],
    "complexity": "simple|medium|complex",
    "database": "sqlite|postgresql|mysql|mongodb|none",
    "authentication": true/false,
    "api_integration": true/false,
    "testing_required": true/false,
    "deployment_target": "local|cloud|docker|none",
    "ui_framework": "bootstrap|tailwind|material|custom|none",
    "project_name": "suggested_name",
    "description": "brief description of the project"
}

Focus on extracting concrete technical requirements from the natural language description.


## Project Type
Web App

## Technology Stack
- **Language**: Python
- **Framework**: Vanilla
- **Complexity**: Simple

## Features
- basic_functionality

## Setup Instructions
# Python Project Setup
1. Create a virtual environment: python -m venv venv
2. Activate virtual environment:
   - Windows: venv\Scripts\activate
   - macOS/Linux: source venv/bin/activate
3. Install dependencies: pip install -r requirements.txt
4. Run the application: python main.py

## Project Structure
```
Web Test/
├── src/
├── public/
├── assets/
├── components/

```

## Usage
[Add usage instructions here]

## Contributing
[Add contributing guidelines here]

## License
[Add license information here]
