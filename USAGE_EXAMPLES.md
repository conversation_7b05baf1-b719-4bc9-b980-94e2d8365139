# AI Code Assistant - Usage Examples

This document provides practical examples of how to use the AI Code Assistant effectively.

## Getting Started

### 1. Installation and Setup

```bash
# Install dependencies
pip install -r requirements.txt

# Configure your API key
cp .env.example .env
# Edit .env and add your Gemini API key
```

### 2. Basic Usage

#### Web Interface
```bash
# Start the web interface
python main.py --mode web

# Open browser to http://localhost:8501
```

#### CLI Interface
```bash
# Start the CLI interface
python main.py --mode cli
```

## Web Interface Examples

### Analyzing a Python File

1. **Select File**: Click on `sample_project.py` in the sidebar
2. **Choose Analysis Type**: Select "Review" from the dropdown
3. **Run Analysis**: Click the "🔍 Analyze" button
4. **Review Suggestions**: Browse through AI suggestions with diff previews
5. **Accept/Reject**: Use the ✅ Accept or ❌ Reject buttons for each suggestion
6. **Apply Changes**: Click "🚀 Apply Changes" for accepted suggestions

### Example Analysis Results

For the sample `sample_project.py` file, you might see suggestions like:

- **Code Quality**: Add type hints to function parameters
- **Performance**: Optimize the fibonacci function with memoization
- **Error Handling**: Add input validation to `process_data` function
- **Documentation**: Improve docstrings with parameter descriptions

## CLI Interface Examples

### Basic Commands

```bash
# List all files in workspace
ai-assistant> ls

# List only Python files
ai-assistant> ls *.py

# Show file content
ai-assistant> show sample_project.py

# Analyze a file for code review
ai-assistant> analyze sample_project.py review

# Analyze for performance optimization
ai-assistant> analyze utils.js optimize
```

### Managing Changes

```bash
# View pending changes
ai-assistant> pending

# Accept a specific change
ai-assistant> accept change_1234567890

# Reject a change with reason
ai-assistant> reject change_1234567890 "Not needed for this version"

# Apply an accepted change
ai-assistant> apply change_1234567890

# View diff for a change
ai-assistant> diff change_1234567890
```

### Project Management

```bash
# Show project summary
ai-assistant> summary

# View analysis history
ai-assistant> history

# Search for specific text in files
ai-assistant> search TODO

# Search for function definitions
ai-assistant> search "def "
```

## Analysis Types and Use Cases

### 1. Code Review (`review`)
**Best for**: General code quality assessment
```bash
analyze myfile.py review
```
**Typical suggestions**:
- Code style improvements
- Potential bugs
- Best practice violations
- Security issues

### 2. Refactoring (`refactor`)
**Best for**: Improving code structure
```bash
analyze legacy_code.py refactor
```
**Typical suggestions**:
- Extract methods/functions
- Reduce code duplication
- Improve naming
- Apply design patterns

### 3. Optimization (`optimize`)
**Best for**: Performance improvements
```bash
analyze slow_function.py optimize
```
**Typical suggestions**:
- Algorithm improvements
- Memory optimization
- Database query optimization
- Caching strategies

### 4. Documentation (`document`)
**Best for**: Adding/improving documentation
```bash
analyze undocumented.py document
```
**Typical suggestions**:
- Add docstrings
- Improve comments
- Add type hints
- Usage examples

### 5. Debugging (`debug`)
**Best for**: Finding and fixing issues
```bash
analyze buggy_code.py debug
```
**Typical suggestions**:
- Error handling improvements
- Edge case handling
- Logging additions
- Input validation

### 6. Code Completion (`complete`)
**Best for**: Finishing incomplete code
```bash
analyze partial_implementation.py complete
```
**Typical suggestions**:
- Complete TODO items
- Implement missing methods
- Add error handling
- Fill in logic gaps

## Advanced Workflows

### 1. Full Project Analysis

```bash
# Analyze all Python files in project
ai-assistant> ls *.py
ai-assistant> analyze file1.py review
ai-assistant> analyze file2.py review
# ... continue for each file

# Or use the web interface to batch analyze
```

### 2. Iterative Improvement

1. Start with code review to identify issues
2. Apply critical fixes first
3. Run optimization analysis
4. Add documentation
5. Final review pass

### 3. Team Code Review Workflow

1. **Developer**: Analyze code before committing
2. **Accept** obvious improvements
3. **Export** remaining suggestions for team review
4. **Discuss** complex changes in team meetings
5. **Apply** agreed-upon changes

## Best Practices

### 1. Start Small
- Begin with single files
- Use review analysis first
- Apply changes incrementally

### 2. Review Before Applying
- Always review diffs carefully
- Test changes in development environment
- Keep backups of important files

### 3. Use Appropriate Analysis Types
- **Review**: For general code health
- **Optimize**: When performance is critical
- **Document**: For public APIs or complex logic
- **Debug**: When investigating issues

### 4. Security Considerations
- The assistant operates within your workspace only
- All changes require explicit approval
- Review suggestions for security implications
- Use version control for important projects

## Troubleshooting

### Common Issues

1. **API Key Errors**
   ```
   Error: Gemini API key is required
   ```
   **Solution**: Check your `.env` file has the correct API key

2. **File Access Errors**
   ```
   Error: Path outside workspace
   ```
   **Solution**: Ensure files are within the configured workspace directory

3. **Large File Warnings**
   ```
   Error: File too large
   ```
   **Solution**: Check `MAX_FILE_SIZE_MB` in configuration

### Getting Help

- Use `help` command in CLI for available commands
- Check the logs for detailed error information
- Review the configuration in `.env` file
- Ensure all dependencies are installed correctly

## Configuration Options

### Environment Variables

```bash
# API Configuration
GEMINI_API_KEY=your_api_key_here

# File System
WORKSPACE_ROOT=./workspace
MAX_FILE_SIZE_MB=10
ALLOWED_EXTENSIONS=.py,.js,.html,.css,.ts,.json,.md,.txt

# Security
ENABLE_FILE_RESTRICTIONS=true
ALLOW_HIDDEN_FILES=false
ALLOW_SYSTEM_FILES=false
```

### Customizing Analysis

You can customize the analysis behavior by:
- Modifying prompts in `core/ai_integration.py`
- Adjusting security settings in `config.py`
- Adding new analysis types
- Configuring file type restrictions

## Integration Examples

### With Version Control

```bash
# Before committing
git add .
python main.py --mode cli
# Analyze changed files
# Apply suggestions
git commit -m "Applied AI suggestions"
```

### With CI/CD

```bash
# In your CI pipeline
python main.py --mode cli
# Run analysis on all files
# Generate report
# Fail build if critical issues found
```

This assistant is designed to enhance your development workflow while maintaining full control over your code changes.
