#!/usr/bin/env python3
"""Test script to verify project folder management functionality."""

from core.analysis_engine import AnalysisEngine
import time

def test_project_folder_management():
    """Test project folder creation and management."""
    print("🧪 Testing project folder management...")
    
    try:
        # Initialize engine
        engine = AnalysisEngine()
        print("✅ AnalysisEngine initialized")
        
        # Test 1: Generate project with folder
        print("\n🚀 Test 1: Generating project with folder...")
        session_id = engine.generate_project(
            prompt="Create a simple Flask web app",
            project_name="my_flask_app",
            target_folder="flask_project_test"
        )
        print(f"✅ Project generated with session ID: {session_id}")
        
        # Apply the project generation
        print("\n📁 Applying project generation...")
        success = engine.apply_project_generation(session_id)
        print(f"✅ Apply result: {success}")
        
        # Test 2: List project folders
        print("\n📋 Test 2: Listing project folders...")
        project_folders = engine.list_project_folders()
        print(f"✅ Found {len(project_folders)} project folders:")
        
        for project in project_folders:
            print(f"  📁 {project['name']}")
            print(f"     - Files: {project['file_count']}")
            print(f"     - Size: {project['total_size']} bytes")
            print(f"     - Type: {project.get('project_type', 'Unknown')}")
        
        # Test 3: Get detailed project info
        if project_folders:
            test_project = project_folders[0]['name']
            print(f"\n🔍 Test 3: Getting details for '{test_project}'...")
            
            project_info = engine.get_project_folder_info(test_project)
            if project_info:
                print(f"✅ Project details loaded:")
                print(f"  - Total files: {project_info['total_files']}")
                print(f"  - Code files: {project_info['code_files']}")
                print(f"  - Config files: {project_info['config_files']}")
                print(f"  - Doc files: {project_info['doc_files']}")
                
                # Show some files
                print(f"  📄 Code files:")
                for file_info in project_info['files_by_type']['code'][:3]:
                    print(f"    - {file_info['name']} ({file_info['size']} bytes)")
            else:
                print("❌ Failed to get project details")
        
        # Test 4: Generate another project
        print("\n🚀 Test 4: Generating second project...")
        session_id2 = engine.generate_project(
            prompt="Create a simple Python CLI tool",
            project_name="cli_tool",
            target_folder="python_cli_test"
        )
        success2 = engine.apply_project_generation(session_id2)
        print(f"✅ Second project created: {success2}")
        
        # List projects again
        print("\n📋 Updated project list:")
        updated_folders = engine.list_project_folders()
        for project in updated_folders:
            print(f"  📁 {project['name']} ({project['file_count']} files)")
        
        print(f"\n🎉 All tests passed! Created {len(updated_folders)} project folders.")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_project_folder_management()
    if success:
        print("\n🎉 Project folder management is working perfectly!")
        print("🌐 You can now test the web interface at http://localhost:8501")
        print("📋 Try the new '📁 Project Manager' button!")
    else:
        print("\n💥 Tests failed! Check the error messages above.")
