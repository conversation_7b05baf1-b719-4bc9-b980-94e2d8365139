#!/usr/bin/env python3
"""Test button functionality with Gemini (free API)."""

from core.analysis_engine import AnalysisEngine

def test_gemini_buttons():
    """Test accept/reject buttons with Gemini."""
    print("🧪 Testing Button Functionality with Gemini")
    print("=" * 50)
    
    try:
        # Initialize engine
        engine = AnalysisEngine()
        print("✅ AnalysisEngine initialized")
        
        # Switch to Gemini (free)
        print("\n⚡ Switching to Gemini (free API)...")
        engine.ai_manager.switch_default_provider("gemini")
        current_provider = engine.ai_manager.default_provider
        print(f"✅ Current provider: {current_provider}")
        
        # Generate a simple project with Gemini
        print(f"\n🚀 Generating project with Gemini...")
        session_id = engine.generate_project(
            prompt="Create a simple Python hello world script",
            project_name="Gemini Test",
            target_folder="gemini_test"
        )
        
        print(f"✅ Project generated with session ID: {session_id}")
        
        # Check the session
        session = engine.get_session(session_id)
        if not session:
            print("❌ Session not found!")
            return False
        
        print(f"📋 Session found with {len(session.change_proposals)} proposals")
        
        # Show proposals (like web interface does)
        for i, proposal in enumerate(session.change_proposals):
            print(f"  {i+1}. {proposal.file_diff.file_path}")
            print(f"     Status: {proposal.status}")
            print(f"     ID: {proposal.id}")
        
        # Test individual accept workflow (like web interface)
        print(f"\n🔄 Testing individual accept workflow...")
        
        first_proposal = session.change_proposals[0]
        print(f"Testing with: {first_proposal.file_diff.file_path}")
        
        # Step 1: Accept (like web accept button)
        print(f"1. Accepting change...")
        engine.diff_manager.accept_change(first_proposal.id)
        
        # Check status after accept
        updated_proposal = engine.diff_manager.get_change_proposal(first_proposal.id)
        print(f"   Status after accept: {updated_proposal.status}")
        
        # Step 2: Apply (like web apply button)
        print(f"2. Applying change...")
        engine.diff_manager.apply_change(first_proposal.id, engine.file_manager)
        
        # Check if file was created
        workspace_root = engine.file_manager.workspace_root
        file_path = workspace_root / first_proposal.file_diff.file_path
        
        if file_path.exists():
            print(f"✅ File created: {file_path}")
            print(f"   Size: {file_path.stat().st_size} bytes")
        else:
            print(f"❌ File not created: {file_path}")
            return False
        
        # Test reject workflow
        print(f"\n🔄 Testing reject workflow...")
        if len(session.change_proposals) > 1:
            second_proposal = session.change_proposals[1]
            print(f"Testing reject with: {second_proposal.file_diff.file_path}")
            
            # Reject (like web reject button)
            engine.diff_manager.reject_change(second_proposal.id, "Test rejection")
            
            # Check status after reject
            updated_proposal = engine.diff_manager.get_change_proposal(second_proposal.id)
            if updated_proposal:
                print(f"   Status after reject: {updated_proposal.status}")
            else:
                print(f"   ✅ Proposal rejected and removed from pending")
        
        # Test "Accept All" workflow (like web Accept All Files button)
        print(f"\n🔄 Testing Accept All workflow...")
        
        # Generate another project for Accept All test
        session_id2 = engine.generate_project(
            prompt="Create a simple Flask web app",
            project_name="Flask Test",
            target_folder="flask_test"
        )
        
        session2 = engine.get_session(session_id2)
        print(f"📋 Accept All test session: {len(session2.change_proposals)} proposals")
        
        # Accept and apply all (like Accept All Files button)
        success_count = 0
        for proposal in session2.change_proposals:
            try:
                # Accept
                engine.diff_manager.accept_change(proposal.id)
                # Apply
                engine.diff_manager.apply_change(proposal.id, engine.file_manager)
                success_count += 1
                print(f"✅ Processed: {proposal.file_diff.file_path}")
            except Exception as e:
                print(f"❌ Failed {proposal.file_diff.file_path}: {e}")
                continue
        
        # Check results
        flask_folder = workspace_root / "flask_test"
        if flask_folder.exists():
            files = list(flask_folder.rglob("*"))
            file_count = len([f for f in files if f.is_file()])
            print(f"✅ Accept All created {file_count} files in flask_test")
        else:
            print(f"❌ Accept All failed - no flask_test folder")
            return False
        
        print(f"\n🎉 All button workflows working with Gemini!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gemini_buttons()
    
    if success:
        print(f"\n🚀 **GEMINI BUTTON TESTS PASSED!**")
        print(f"✅ Individual accept/reject: Working")
        print(f"✅ Accept All workflow: Working")
        print(f"✅ File creation: Working")
        print(f"⚡ Using Gemini (free API)")
        print(f"\n🔧 Now fixing web interface buttons...")
    else:
        print(f"\n💥 Gemini button tests failed - check errors above")
