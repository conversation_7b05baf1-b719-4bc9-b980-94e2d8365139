"""Command-line interface for the AI Code Assistant."""

import os
import sys
import time
from pathlib import Path
from typing import List, Optional, Dict, Any

import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.syntax import Syntax
from rich.progress import Progress, SpinnerColumn, TextColumn

# Import core components
from core.analysis_engine import AnalysisEngine
from core.ai_integration import AnalysisType
from core.diff_manager import ChangeStatus
from config import Config

console = Console()

class CLIInterface:
    """Command-line interface for the AI Code Assistant."""
    
    def __init__(self):
        """Initialize CLI interface."""
        try:
            self.engine = AnalysisEngine()
            self.current_session = None
            console.print("✅ AI Code Assistant initialized successfully", style="green")
        except Exception as e:
            console.print(f"❌ Initialization failed: {e}", style="red")
            sys.exit(1)
    
    def run(self):
        """Run the main CLI loop."""
        console.print(Panel(
            "🤖 Welcome to AI Code Assistant\n"
            "Type 'help' for available commands or 'quit' to exit.",
            title="AI Code Assistant",
            border_style="blue"
        ))
        
        while True:
            try:
                command = Prompt.ask("\n[bold blue]ai-assistant[/bold blue]", default="help")
                
                if command.lower() in ['quit', 'exit', 'q']:
                    console.print("👋 Goodbye!", style="green")
                    break
                
                self.handle_command(command)
                
            except KeyboardInterrupt:
                console.print("\n👋 Goodbye!", style="green")
                break
            except Exception as e:
                console.print(f"❌ Error: {e}", style="red")
    
    def handle_command(self, command: str):
        """Handle a CLI command."""
        parts = command.strip().split()
        if not parts:
            return
        
        cmd = parts[0].lower()
        args = parts[1:]
        
        commands = {
            'help': self.show_help,
            'ls': self.list_files,
            'analyze': self.analyze_file,
            'show': self.show_file,
            'pending': self.show_pending_changes,
            'accept': self.accept_change,
            'reject': self.reject_change,
            'apply': self.apply_change,
            'summary': self.show_project_summary,
            'history': self.show_analysis_history,
            'diff': self.show_diff,
            'search': self.search_files,
            'generate': self.generate_project,
            'projects': self.show_project_changes,
            'preview': self.preview_project
        }
        
        if cmd in commands:
            try:
                commands[cmd](args)
            except Exception as e:
                console.print(f"❌ Command failed: {e}", style="red")
        else:
            console.print(f"❌ Unknown command: {cmd}. Type 'help' for available commands.", style="red")
    
    def show_help(self, args: List[str]):
        """Show help information."""
        help_table = Table(title="Available Commands")
        help_table.add_column("Command", style="cyan", no_wrap=True)
        help_table.add_column("Description", style="white")
        help_table.add_column("Example", style="green")
        
        commands = [
            ("help", "Show this help message", "help"),
            ("ls [pattern]", "List files in workspace", "ls *.py"),
            ("analyze <file> [type]", "Analyze a file", "analyze main.py review"),
            ("show <file>", "Show file content", "show config.py"),
            ("pending", "Show pending changes", "pending"),
            ("accept <change_id>", "Accept a change", "accept change_123"),
            ("reject <change_id>", "Reject a change", "reject change_123"),
            ("apply <change_id>", "Apply accepted change", "apply change_123"),
            ("generate <description>", "Generate a project", "generate 'Flask web app with auth'"),
            ("projects", "Show project changes", "projects"),
            ("preview <project_id>", "Preview project structure", "preview project_123"),
            ("summary", "Show project summary", "summary"),
            ("history", "Show analysis history", "history"),
            ("diff <change_id>", "Show change diff", "diff change_123"),
            ("search <query>", "Search in files", "search TODO"),
            ("quit", "Exit the application", "quit")
        ]
        
        for cmd, desc, example in commands:
            help_table.add_row(cmd, desc, example)
        
        console.print(help_table)
    
    def list_files(self, args: List[str]):
        """List files in the workspace."""
        try:
            pattern = args[0] if args else "*"
            files = self.engine.file_manager.list_files(recursive=True)
            
            # Filter files
            if pattern != "*":
                files = [f for f in files if Path(f.name).match(pattern)]
            
            # Filter text files only
            text_files = [f for f in files if f.is_text and not f.is_directory]
            
            if not text_files:
                console.print("No files found matching pattern.", style="yellow")
                return
            
            # Create table
            table = Table(title=f"Files (pattern: {pattern})")
            table.add_column("Path", style="cyan")
            table.add_column("Size", style="white", justify="right")
            table.add_column("Type", style="green")
            table.add_column("Modified", style="yellow")
            
            for file_info in sorted(text_files, key=lambda f: str(f.path)):
                size_str = f"{file_info.size:,} bytes"
                modified_str = time.strftime('%Y-%m-%d %H:%M', time.localtime(file_info.last_modified))
                table.add_row(
                    str(file_info.path),
                    size_str,
                    file_info.extension or "no ext",
                    modified_str
                )
            
            console.print(table)
            
        except Exception as e:
            console.print(f"❌ Error listing files: {e}", style="red")
    
    def analyze_file(self, args: List[str]):
        """Analyze a file."""
        if not args:
            console.print("❌ Please specify a file to analyze", style="red")
            return
        
        file_path = args[0]
        analysis_type_str = args[1] if len(args) > 1 else "review"
        
        try:
            analysis_type = AnalysisType(analysis_type_str.lower())
        except ValueError:
            console.print(f"❌ Invalid analysis type: {analysis_type_str}", style="red")
            console.print(f"Available types: {', '.join([t.value for t in AnalysisType])}", style="yellow")
            return
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task(f"Analyzing {file_path}...", total=None)
                
                session = self.engine.analyze_file(file_path, analysis_type)
                self.current_session = session
                
                progress.update(task, completed=True)
            
            # Show results
            console.print(f"✅ Analysis completed!", style="green")
            console.print(f"📊 Summary: {session.ai_result.summary}")
            console.print(f"💡 Found {len(session.change_proposals)} suggestions")
            console.print(f"⏱️  Analysis time: {session.ai_result.analysis_time:.2f}s")
            
            if session.change_proposals:
                console.print("\n📋 Change Proposals:")
                self._show_proposals_table(session.change_proposals)
                console.print("\nUse 'accept <id>' or 'reject <id>' to manage changes")
            
        except Exception as e:
            console.print(f"❌ Analysis failed: {e}", style="red")
    
    def show_file(self, args: List[str]):
        """Show file content."""
        if not args:
            console.print("❌ Please specify a file to show", style="red")
            return
        
        file_path = args[0]
        
        try:
            content = self.engine.file_manager.read_file(file_path)
            file_info = self.engine.file_manager.get_file_info(file_path)
            
            # Determine language for syntax highlighting
            language = self._get_language_from_extension(file_info.extension)
            
            syntax = Syntax(content, language, theme="monokai", line_numbers=True)
            console.print(Panel(syntax, title=f"📄 {file_path}", border_style="blue"))
            
        except Exception as e:
            console.print(f"❌ Error reading file: {e}", style="red")
    
    def show_pending_changes(self, args: List[str]):
        """Show pending changes."""
        try:
            pending_changes = self.engine.diff_manager.get_pending_changes()
            
            if not pending_changes:
                console.print("No pending changes.", style="yellow")
                return
            
            console.print(f"📋 Found {len(pending_changes)} pending changes:")
            self._show_proposals_table(pending_changes)
            
        except Exception as e:
            console.print(f"❌ Error loading pending changes: {e}", style="red")
    
    def accept_change(self, args: List[str]):
        """Accept a change proposal."""
        if not args:
            console.print("❌ Please specify a change ID", style="red")
            return
        
        change_id = args[0]
        
        try:
            self.engine.diff_manager.accept_change(change_id)
            console.print(f"✅ Change {change_id} accepted! Use 'apply {change_id}' to save to file.", style="green")
            
        except Exception as e:
            console.print(f"❌ Error accepting change: {e}", style="red")
    
    def reject_change(self, args: List[str]):
        """Reject a change proposal."""
        if not args:
            console.print("❌ Please specify a change ID", style="red")
            return
        
        change_id = args[0]
        reason = " ".join(args[1:]) if len(args) > 1 else ""
        
        try:
            self.engine.diff_manager.reject_change(change_id, reason)
            console.print(f"❌ Change {change_id} rejected.", style="yellow")
            
        except Exception as e:
            console.print(f"❌ Error rejecting change: {e}", style="red")
    
    def apply_change(self, args: List[str]):
        """Apply an accepted change."""
        if not args:
            console.print("❌ Please specify a change ID", style="red")
            return
        
        change_id = args[0]
        
        try:
            proposal = self.engine.diff_manager.get_change_proposal(change_id)
            if not proposal:
                console.print(f"❌ Change {change_id} not found", style="red")
                return
            
            if proposal.status != ChangeStatus.ACCEPTED:
                console.print(f"❌ Change {change_id} is not accepted (status: {proposal.status.value})", style="red")
                return
            
            # Confirm before applying
            if Confirm.ask(f"Apply changes to {proposal.file_diff.file_path}?"):
                self.engine.diff_manager.apply_change(change_id, self.engine.file_manager)
                console.print(f"✅ Changes applied to {proposal.file_diff.file_path}!", style="green")
            else:
                console.print("Operation cancelled.", style="yellow")
                
        except Exception as e:
            console.print(f"❌ Error applying change: {e}", style="red")
    
    def show_project_summary(self, args: List[str]):
        """Show project summary."""
        try:
            summary = self.engine.get_project_summary()
            
            # Create summary panel
            summary_text = []
            
            if summary.get('project_type'):
                summary_text.append(f"🏗️  Project Type: {summary['project_type']}")
            
            summary_text.append(f"📁 Workspace: {summary['workspace_root']}")
            
            file_stats = summary.get('file_stats', {})
            if file_stats:
                summary_text.append(f"📄 Total Files: {file_stats.get('total_files', 0)}")
                summary_text.append(f"📂 Directories: {file_stats.get('total_directories', 0)}")
            
            analysis_stats = summary.get('analysis_stats', {})
            if analysis_stats:
                summary_text.append(f"🔍 Analysis Sessions: {analysis_stats.get('total_sessions', 0)}")
                summary_text.append(f"⏳ Pending Changes: {analysis_stats.get('pending_changes', 0)}")
            
            console.print(Panel(
                "\n".join(summary_text),
                title="📊 Project Summary",
                border_style="green"
            ))
            
            # Show file type breakdown
            file_types = file_stats.get('file_types', {})
            if file_types:
                table = Table(title="File Types")
                table.add_column("Extension", style="cyan")
                table.add_column("Count", style="white", justify="right")
                
                for ext, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True):
                    table.add_row(ext or "no extension", str(count))
                
                console.print(table)
            
        except Exception as e:
            console.print(f"❌ Error loading project summary: {e}", style="red")
    
    def show_analysis_history(self, args: List[str]):
        """Show analysis history."""
        try:
            limit = int(args[0]) if args and args[0].isdigit() else 10
            sessions = self.engine.get_recent_sessions(limit)
            
            if not sessions:
                console.print("No analysis sessions found.", style="yellow")
                return
            
            table = Table(title=f"Recent Analysis Sessions (last {limit})")
            table.add_column("File", style="cyan")
            table.add_column("Type", style="green")
            table.add_column("Suggestions", style="white", justify="right")
            table.add_column("Time", style="yellow")
            
            for session in sessions:
                time_str = time.strftime('%Y-%m-%d %H:%M', time.localtime(session.created_at))
                table.add_row(
                    session.file_path,
                    session.analysis_type.value,
                    str(len(session.change_proposals)),
                    time_str
                )
            
            console.print(table)
            
        except Exception as e:
            console.print(f"❌ Error loading analysis history: {e}", style="red")
    
    def show_diff(self, args: List[str]):
        """Show diff for a change proposal."""
        if not args:
            console.print("❌ Please specify a change ID", style="red")
            return
        
        change_id = args[0]
        
        try:
            proposal = self.engine.diff_manager.get_change_proposal(change_id)
            if not proposal:
                console.print(f"❌ Change {change_id} not found", style="red")
                return
            
            diff_display = self.engine.diff_manager.format_diff_for_display(proposal.file_diff)
            
            console.print(Panel(
                f"[bold]{proposal.title}[/bold]\n\n{proposal.description}",
                title=f"Change {change_id}",
                border_style="blue"
            ))
            
            syntax = Syntax(diff_display, "diff", theme="monokai", line_numbers=False)
            console.print(syntax)
            
        except Exception as e:
            console.print(f"❌ Error showing diff: {e}", style="red")
    
    def search_files(self, args: List[str]):
        """Search for text in files."""
        if not args:
            console.print("❌ Please specify a search query", style="red")
            return
        
        query = " ".join(args)
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task(f"Searching for '{query}'...", total=None)
                
                results = self.engine.file_manager.search_files(query)
                
                progress.update(task, completed=True)
            
            if not results:
                console.print(f"No results found for '{query}'", style="yellow")
                return
            
            console.print(f"🔍 Found {len(results)} files with matches:")
            
            for file_info, matches in results:
                console.print(f"\n📄 [cyan]{file_info.path}[/cyan] ({len(matches)} matches)")
                
                for line_num, line_content in matches[:5]:  # Show first 5 matches per file
                    console.print(f"  [yellow]{line_num:4d}[/yellow]: {line_content}")
                
                if len(matches) > 5:
                    console.print(f"  ... and {len(matches) - 5} more matches")
            
        except Exception as e:
            console.print(f"❌ Search failed: {e}", style="red")
    
    def _show_proposals_table(self, proposals):
        """Show change proposals in a table."""
        table = Table()
        table.add_column("ID", style="cyan", no_wrap=True)
        table.add_column("Title", style="white")
        table.add_column("File", style="green")
        table.add_column("Type", style="yellow")
        table.add_column("Confidence", style="blue", justify="right")
        table.add_column("Status", style="magenta")
        
        for proposal in proposals:
            status_emoji = {"pending": "⏳", "accepted": "✅", "rejected": "❌", "applied": "🚀"}
            status_display = f"{status_emoji.get(proposal.status.value, '❓')} {proposal.status.value}"
            
            table.add_row(
                proposal.id[-8:],  # Show last 8 chars of ID
                proposal.title[:40] + "..." if len(proposal.title) > 40 else proposal.title,
                proposal.file_diff.file_path,
                proposal.suggestion_type,
                f"{proposal.confidence:.1%}",
                status_display
            )
        
        console.print(table)
    
    def _get_language_from_extension(self, extension: str) -> str:
        """Get syntax highlighting language from file extension."""
        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.html': 'html',
            '.css': 'css',
            '.json': 'json',
            '.xml': 'xml',
            '.sql': 'sql',
            '.sh': 'bash',
            '.bat': 'batch',
            '.md': 'markdown',
            '.yml': 'yaml',
            '.yaml': 'yaml'
        }
        return language_map.get(extension.lower() if extension else '', 'text')

    def generate_project(self, args: List[str]):
        """Generate a new project from description."""
        if not args:
            console.print("❌ Please provide a project description", style="red")
            console.print("Example: generate 'Flask web app with user authentication'", style="yellow")
            return

        description = " ".join(args)

        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task(f"Generating project: {description}...", total=None)

                session_id = self.engine.generate_project(description)

                progress.update(task, completed=True)

            # Show project preview
            preview = self.engine.get_project_structure_preview(session_id)
            if preview:
                console.print(f"✅ Project generated successfully!", style="green")
                console.print(f"📊 Project: {preview['project_name']}")
                console.print(f"📁 Files: {preview['total_files']}")
                console.print(f"📋 Summary: {preview['summary']}")

                # Show file structure
                console.print("\n📂 Project Structure:")
                for directory, files in preview['directories'].items():
                    console.print(f"  📁 {directory}/")
                    for file_info in files:
                        size_kb = file_info['size'] / 1024
                        console.print(f"    📄 {file_info['name']} ({size_kb:.1f}KB)")

                console.print(f"\nUse 'preview {session_id}' to see detailed structure")
                console.print(f"Use 'apply {session_id}' to create the project files")

        except Exception as e:
            console.print(f"❌ Project generation failed: {e}", style="red")

    def show_project_changes(self, args: List[str]):
        """Show pending project changes."""
        try:
            # Get project changes from analysis sessions
            recent_sessions = self.engine.get_recent_sessions(20)
            project_sessions = [
                s for s in recent_sessions
                if s.analysis_type.value == "project_generation"
            ]

            if not project_sessions:
                console.print("No project generation sessions found.", style="yellow")
                return

            table = Table(title="Project Generation Sessions")
            table.add_column("Session ID", style="cyan", no_wrap=True)
            table.add_column("Project", style="white")
            table.add_column("Files", style="green", justify="right")
            table.add_column("Status", style="yellow")
            table.add_column("Created", style="blue")

            for session in project_sessions:
                project_name = session.file_path.replace('PROJECT:', '')
                file_count = len(session.change_proposals)

                # Determine status based on change proposals
                if all(p.status.value == "applied" for p in session.change_proposals):
                    status = "✅ Applied"
                elif any(p.status.value == "accepted" for p in session.change_proposals):
                    status = "🟡 Accepted"
                elif any(p.status.value == "rejected" for p in session.change_proposals):
                    status = "❌ Rejected"
                else:
                    status = "⏳ Pending"

                created_time = time.strftime('%Y-%m-%d %H:%M', time.localtime(session.created_at))

                table.add_row(
                    session.session_id[-8:],  # Show last 8 chars
                    project_name,
                    str(file_count),
                    status,
                    created_time
                )

            console.print(table)

        except Exception as e:
            console.print(f"❌ Error loading project changes: {e}", style="red")

    def preview_project(self, args: List[str]):
        """Preview a generated project structure."""
        if not args:
            console.print("❌ Please specify a session ID", style="red")
            return

        session_id = args[0]

        try:
            preview = self.engine.get_project_structure_preview(session_id)
            if not preview:
                console.print(f"❌ Project session {session_id} not found", style="red")
                return

            # Show detailed project information
            console.print(Panel(
                f"[bold]{preview['project_name']}[/bold]\n\n"
                f"📁 Total Files: {preview['total_files']}\n"
                f"📊 Summary: {preview['summary']}\n"
                f"🔗 Dependencies: {', '.join(preview.get('dependencies', []))}",
                title=f"Project Preview - {session_id[-8:]}",
                border_style="blue"
            ))

            # Show detailed file structure
            for directory, files in preview['directories'].items():
                console.print(f"\n📁 [bold cyan]{directory}/[/bold cyan]")

                for file_info in files:
                    size_kb = file_info['size'] / 1024
                    console.print(f"  📄 {file_info['name']} ({size_kb:.1f}KB)")
                    console.print(f"      {file_info['description']}")

            # Show action options
            console.print(f"\n[bold]Available Actions:[/bold]")
            console.print(f"• [green]apply {session_id}[/green] - Create all project files")
            console.print(f"• [yellow]diff <change_id>[/yellow] - View individual file content")
            console.print(f"• [red]reject {session_id}[/red] - Reject the entire project")

        except Exception as e:
            console.print(f"❌ Error previewing project: {e}", style="red")
