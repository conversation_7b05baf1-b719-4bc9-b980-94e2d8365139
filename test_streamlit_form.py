#!/usr/bin/env python3
"""Test Streamlit form submission issue."""

import streamlit as st
import time

def test_form():
    """Test basic form submission."""
    st.title("🧪 Form Submission Test")
    
    # Test basic form
    with st.form("test_form"):
        st.write("### Test Form")
        
        text_input = st.text_area(
            "Enter some text:",
            placeholder="Type something here...",
            height=100
        )
        
        name_input = st.text_input(
            "Name:",
            placeholder="Your name"
        )
        
        submitted = st.form_submit_button("🎯 Test Submit", type="primary")
        
        # Debug form submission
        if submitted:
            st.write("🔥 **FORM SUBMITTED!**")
            st.write(f"📝 Text provided: {bool(text_input)}")
            st.write(f"📝 Text content: '{text_input}'")
            st.write(f"🏷️ Name: '{name_input}'")
            
            if text_input:
                st.success("✅ Form submission successful!")
                st.balloons()
            else:
                st.error("❌ Please provide some text")
        elif submitted:
            st.write("🔥 **BUTTON CLICKED BUT NO TEXT!**")
    
    # Test session state
    st.write("### Session State Test")
    if st.button("🔄 Test Session State"):
        st.session_state.test_value = f"Updated at {time.time()}"
        st.write(f"✅ Session state updated: {st.session_state.test_value}")
        st.rerun()
    
    if hasattr(st.session_state, 'test_value'):
        st.write(f"📋 Current session value: {st.session_state.test_value}")

if __name__ == "__main__":
    test_form()
