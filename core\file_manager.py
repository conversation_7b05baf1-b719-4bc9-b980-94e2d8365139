"""File management system with security boundaries and multi-format support."""

import os
import mimetypes
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass
from config import Config
import logging

logger = logging.getLogger(__name__)

@dataclass
class FileInfo:
    """Information about a file."""
    path: Path
    name: str
    size: int
    extension: str
    mime_type: Optional[str]
    is_text: bool
    last_modified: float
    is_directory: bool

class FileManager:
    """Manages file operations with security boundaries."""
    
    def __init__(self, workspace_root: Optional[Path] = None):
        """Initialize file manager with workspace root."""
        self.workspace_root = workspace_root or Config.WORKSPACE_ROOT
        self.workspace_root = self.workspace_root.resolve()
        
        # Ensure workspace exists
        self.workspace_root.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"FileManager initialized with workspace: {self.workspace_root}")
    
    def _validate_path(self, path: Path) -> Path:
        """Validate and resolve a path within workspace boundaries."""
        try:
            # Convert to absolute path and resolve
            abs_path = (self.workspace_root / path).resolve()
            
            # Ensure path is within workspace
            abs_path.relative_to(self.workspace_root)
            
            # Check against forbidden paths
            if not Config.is_path_allowed(abs_path):
                raise PermissionError(f"Access denied to path: {path}")
                
            return abs_path
            
        except ValueError:
            raise PermissionError(f"Path outside workspace: {path}")
        except Exception as e:
            raise PermissionError(f"Invalid path: {path} - {e}")
    
    def list_files(self, directory: str = ".", recursive: bool = False, 
                   include_hidden: bool = False) -> List[FileInfo]:
        """List files in a directory with optional recursion."""
        try:
            dir_path = self._validate_path(Path(directory))
            
            if not dir_path.exists():
                return []
                
            if not dir_path.is_dir():
                raise ValueError(f"Path is not a directory: {directory}")
            
            files = []
            pattern = "**/*" if recursive else "*"
            
            for item in dir_path.glob(pattern):
                # Skip hidden files unless explicitly allowed
                if not include_hidden and not Config.ALLOW_HIDDEN_FILES:
                    if any(part.startswith('.') for part in item.parts):
                        continue
                
                try:
                    # Get file info
                    stat = item.stat()
                    mime_type, _ = mimetypes.guess_type(str(item))
                    
                    # Determine if file is text-based
                    is_text = self._is_text_file(item, mime_type)
                    
                    file_info = FileInfo(
                        path=item.relative_to(self.workspace_root),
                        name=item.name,
                        size=stat.st_size if item.is_file() else 0,
                        extension=item.suffix.lower(),
                        mime_type=mime_type,
                        is_text=is_text,
                        last_modified=stat.st_mtime,
                        is_directory=item.is_dir()
                    )
                    
                    files.append(file_info)
                    
                except (OSError, PermissionError) as e:
                    logger.warning(f"Cannot access {item}: {e}")
                    continue
            
            # Sort: directories first, then by name
            files.sort(key=lambda f: (not f.is_directory, f.name.lower()))
            return files
            
        except Exception as e:
            logger.error(f"Error listing files in {directory}: {e}")
            raise
    
    def read_file(self, file_path: str, encoding: str = "utf-8") -> str:
        """Read file content with encoding detection."""
        try:
            abs_path = self._validate_path(Path(file_path))
            
            if not abs_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
                
            if not abs_path.is_file():
                raise ValueError(f"Path is not a file: {file_path}")
            
            # Check file size
            if abs_path.stat().st_size > Config.MAX_FILE_SIZE_BYTES:
                raise ValueError(f"File too large: {file_path} (max {Config.MAX_FILE_SIZE_MB}MB)")
            
            # Check extension if restrictions enabled
            if not Config.is_extension_allowed(abs_path):
                raise PermissionError(f"File type not allowed: {abs_path.suffix}")
            
            # Try to read with specified encoding
            try:
                with open(abs_path, 'r', encoding=encoding) as f:
                    return f.read()
            except UnicodeDecodeError:
                # Try common encodings
                for enc in ['utf-8', 'latin-1', 'cp1252']:
                    try:
                        with open(abs_path, 'r', encoding=enc) as f:
                            content = f.read()
                            logger.info(f"Successfully read {file_path} with encoding {enc}")
                            return content
                    except UnicodeDecodeError:
                        continue
                
                raise ValueError(f"Cannot decode file {file_path} with any common encoding")
                
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            raise
    
    def write_file(self, file_path: str, content: str, encoding: str = "utf-8", 
                   create_dirs: bool = True) -> bool:
        """Write content to a file."""
        try:
            abs_path = self._validate_path(Path(file_path))
            
            # Check extension if restrictions enabled
            if not Config.is_extension_allowed(abs_path):
                raise PermissionError(f"File type not allowed: {abs_path.suffix}")
            
            # Create parent directories if needed
            if create_dirs:
                abs_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write file
            with open(abs_path, 'w', encoding=encoding) as f:
                f.write(content)
            
            logger.info(f"Successfully wrote file: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error writing file {file_path}: {e}")
            raise
    
    def delete_file(self, file_path: str) -> bool:
        """Delete a file."""
        try:
            abs_path = self._validate_path(Path(file_path))
            
            if not abs_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            if abs_path.is_file():
                abs_path.unlink()
            elif abs_path.is_dir():
                abs_path.rmdir()  # Only remove empty directories
            else:
                raise ValueError(f"Cannot delete: {file_path}")
            
            logger.info(f"Successfully deleted: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting {file_path}: {e}")
            raise
    
    def create_directory(self, dir_path: str) -> bool:
        """Create a directory."""
        try:
            abs_path = self._validate_path(Path(dir_path))
            abs_path.mkdir(parents=True, exist_ok=True)
            
            logger.info(f"Successfully created directory: {dir_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating directory {dir_path}: {e}")
            raise
    
    def get_file_info(self, file_path: str) -> FileInfo:
        """Get detailed information about a file."""
        try:
            abs_path = self._validate_path(Path(file_path))
            
            if not abs_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            stat = abs_path.stat()
            mime_type, _ = mimetypes.guess_type(str(abs_path))
            is_text = self._is_text_file(abs_path, mime_type)
            
            return FileInfo(
                path=abs_path.relative_to(self.workspace_root),
                name=abs_path.name,
                size=stat.st_size if abs_path.is_file() else 0,
                extension=abs_path.suffix.lower(),
                mime_type=mime_type,
                is_text=is_text,
                last_modified=stat.st_mtime,
                is_directory=abs_path.is_dir()
            )
            
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {e}")
            raise
    
    def _is_text_file(self, path: Path, mime_type: Optional[str]) -> bool:
        """Determine if a file is text-based."""
        # Check by MIME type
        if mime_type:
            if mime_type.startswith('text/'):
                return True
            if mime_type in ['application/json', 'application/xml', 'application/javascript']:
                return True
        
        # Check by extension
        text_extensions = {
            '.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml',
            '.yml', '.yaml', '.sql', '.sh', '.bat', '.ps1', '.ts', '.jsx',
            '.tsx', '.vue', '.php', '.rb', '.go', '.rs', '.java', '.c',
            '.cpp', '.h', '.hpp', '.cs', '.swift', '.kt', '.scala', '.r'
        }
        
        if path.suffix.lower() in text_extensions:
            return True
        
        # Try to read a small portion to detect binary
        try:
            with open(path, 'rb') as f:
                chunk = f.read(1024)
                # Check for null bytes (common in binary files)
                return b'\x00' not in chunk
        except:
            return False
    
    def search_files(self, query: str, file_pattern: str = "*", 
                     case_sensitive: bool = False) -> List[Tuple[FileInfo, List[Tuple[int, str]]]]:
        """Search for text within files."""
        results = []
        
        try:
            files = self.list_files(recursive=True)
            
            for file_info in files:
                if not file_info.is_text or file_info.is_directory:
                    continue
                
                # Check if file matches pattern
                if not Path(file_info.name).match(file_pattern):
                    continue
                
                try:
                    content = self.read_file(str(file_info.path))
                    matches = []
                    
                    lines = content.split('\n')
                    search_query = query if case_sensitive else query.lower()
                    
                    for line_num, line in enumerate(lines, 1):
                        search_line = line if case_sensitive else line.lower()
                        if search_query in search_line:
                            matches.append((line_num, line.strip()))
                    
                    if matches:
                        results.append((file_info, matches))
                        
                except Exception as e:
                    logger.warning(f"Error searching in {file_info.path}: {e}")
                    continue
            
            return results
            
        except Exception as e:
            logger.error(f"Error during file search: {e}")
            raise
