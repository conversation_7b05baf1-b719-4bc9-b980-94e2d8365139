#!/usr/bin/env python3
"""Debug script to test the accept button functionality."""

from core.analysis_engine import Analysis<PERSON>ng<PERSON>

def debug_accept_button():
    """Debug the accept button functionality."""
    print("🔍 Debugging accept button functionality...")
    
    try:
        # Initialize engine
        engine = AnalysisEngine()
        print("✅ AnalysisEngine initialized")
        
        # Generate a simple project
        print("\n🚀 Generating test project...")
        session_id = engine.generate_project(
            prompt="Create a simple Python hello world script",
            project_name="Debug Test",
            target_folder="debug_test"
        )
        
        print(f"✅ Project generated with session ID: {session_id}")
        
        # Check the session
        session = engine.get_session(session_id)
        if not session:
            print("❌ Session not found!")
            return False
        
        print(f"📋 Session found with {len(session.change_proposals)} proposals")
        
        # Show proposals
        for i, proposal in enumerate(session.change_proposals):
            print(f"  {i+1}. {proposal.file_diff.file_path}")
            print(f"     Status: {proposal.status}")
            print(f"     Content length: {len(proposal.file_diff.new_content)} chars")
        
        # Try to apply the project
        print(f"\n📁 Applying project generation...")
        
        # Check workspace before
        workspace_files_before = list(engine.file_manager.workspace_root.glob("debug_test/*"))
        print(f"📂 Files in debug_test before: {len(workspace_files_before)}")
        
        # Apply
        success = engine.apply_project_generation(session_id)
        print(f"✅ Apply result: {success}")
        
        # Check workspace after
        workspace_files_after = list(engine.file_manager.workspace_root.glob("debug_test/*"))
        print(f"📂 Files in debug_test after: {len(workspace_files_after)}")
        
        if workspace_files_after:
            print("📄 Created files:")
            for file_path in workspace_files_after:
                if file_path.is_file():
                    print(f"  - {file_path.name} ({file_path.stat().st_size} bytes)")
        
        # Check proposal statuses after apply
        session_after = engine.get_session(session_id)
        if session_after:
            print(f"\n📋 Proposal statuses after apply:")
            for i, proposal in enumerate(session_after.change_proposals):
                print(f"  {i+1}. {proposal.file_diff.file_path}: {proposal.status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_diff_manager():
    """Debug the diff manager functionality."""
    print("\n🔍 Debugging diff manager...")
    
    try:
        from core.diff_manager import DiffManager
        from core.file_manager import FileManager
        
        file_manager = FileManager()
        diff_manager = DiffManager()
        
        # Create a test change proposal
        from core.diff_manager import FileDiff, ChangeProposal
        
        test_file_diff = FileDiff(
            file_path="debug_test/test.py",
            old_content="",
            new_content="print('Hello, World!')\n"
        )
        
        proposal = diff_manager.create_change_proposal(
            title="Create test.py",
            description="Test file creation",
            old_content="",
            new_content="print('Hello, World!')\n",
            file_path="debug_test/test.py",
            confidence=1.0,
            reasoning="Test",
            suggestion_type="file_creation"
        )
        
        print(f"✅ Created test proposal: {proposal.id}")
        print(f"📄 File path: {proposal.file_diff.file_path}")
        print(f"📝 Content: {proposal.file_diff.new_content}")
        
        # Try to accept and apply
        print(f"\n🔄 Accepting change...")
        diff_manager.accept_change(proposal.id)
        
        print(f"📁 Applying change...")
        diff_manager.apply_change(proposal.id, file_manager)
        
        # Check if file was created
        test_file_path = file_manager.workspace_root / "debug_test" / "test.py"
        if test_file_path.exists():
            print(f"✅ File created successfully: {test_file_path}")
            with open(test_file_path, 'r') as f:
                content = f.read()
            print(f"📄 Content: {repr(content)}")
        else:
            print(f"❌ File not created: {test_file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Diff manager error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Debugging Accept Button Functionality")
    print("=" * 50)
    
    # Test the full workflow
    workflow_success = debug_accept_button()
    
    if not workflow_success:
        print("\n🔧 Testing diff manager directly...")
        diff_success = debug_diff_manager()
        
        if diff_success:
            print("\n✅ Diff manager works - issue is in project generation workflow")
        else:
            print("\n❌ Diff manager has issues")
    else:
        print("\n🎉 Accept button functionality is working!")
