"""Project generation system for creating complete projects from natural language prompts."""

import logging
import json
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import time

from .ai_integration import GeminiIntegration
from .file_manager import FileManager
from .diff_manager import DiffManager, ChangeProposal

logger = logging.getLogger(__name__)

class ProjectType(Enum):
    """Types of projects that can be generated."""
    WEB_APP = "web_app"
    API = "api"
    DESKTOP_APP = "desktop_app"
    DATA_SCIENCE = "data_science"
    MOBILE_APP = "mobile_app"
    CLI_TOOL = "cli_tool"
    LIBRARY = "library"
    GAME = "game"
    AUTOMATION = "automation"
    MICROSERVICE = "microservice"

class Framework(Enum):
    """Supported frameworks and technologies."""
    # Web Frameworks
    REACT = "react"
    ANGULAR = "angular"
    VUE = "vue"
    FLASK = "flask"
    DJANGO = "django"
    FASTAPI = "fastapi"
    EXPRESS = "express"
    NEXTJS = "nextjs"
    
    # Desktop
    TKINTER = "tkinter"
    PYQT = "pyqt"
    ELECTRON = "electron"
    
    # Data Science
    JUPYTER = "jupyter"
    STREAMLIT = "streamlit"
    DASH = "dash"
    
    # Mobile
    REACT_NATIVE = "react_native"
    FLUTTER = "flutter"
    
    # Other
    VANILLA = "vanilla"
    CUSTOM = "custom"

@dataclass
class ProjectFile:
    """Represents a file to be generated in the project."""
    path: str
    content: str
    description: str
    file_type: str
    is_executable: bool = False
    dependencies: List[str] = None

@dataclass
class ProjectStructure:
    """Complete project structure with files and metadata."""
    name: str
    description: str
    project_type: ProjectType
    framework: Framework
    language: str
    files: List[ProjectFile]
    directories: List[str]
    dependencies: Dict[str, List[str]]  # package_manager -> [dependencies]
    setup_instructions: List[str]
    estimated_complexity: str  # "simple", "medium", "complex"

@dataclass
class ProjectGenerationRequest:
    """Request for project generation."""
    prompt: str
    project_name: Optional[str] = None
    preferred_framework: Optional[Framework] = None
    preferred_language: Optional[str] = None
    include_tests: bool = True
    include_docs: bool = True
    target_directory: str = "."

class ProjectGenerator:
    """Generates complete projects from natural language descriptions."""
    
    def __init__(self, file_manager: FileManager, ai_integration: GeminiIntegration):
        """Initialize project generator."""
        self.file_manager = file_manager
        self.ai_integration = ai_integration
        
        # Project templates and patterns
        self.project_templates = self._load_project_templates()
        self.framework_patterns = self._load_framework_patterns()
        
        logger.info("ProjectGenerator initialized")
    
    def generate_project(self, request: ProjectGenerationRequest) -> ProjectStructure:
        """Generate a complete project from a natural language prompt."""
        try:
            logger.info(f"Generating project from prompt: {request.prompt}")
            
            # Parse the request to understand requirements
            project_analysis = self._analyze_project_requirements(request)
            
            # Generate project structure
            project_structure = self._generate_project_structure(project_analysis, request)
            
            # Generate individual files
            self._generate_project_files(project_structure, project_analysis)
            
            logger.info(f"Generated project '{project_structure.name}' with {len(project_structure.files)} files")
            return project_structure
            
        except Exception as e:
            logger.error(f"Error generating project: {e}")
            raise
    
    def _analyze_project_requirements(self, request: ProjectGenerationRequest) -> Dict[str, Any]:
        """Analyze the natural language prompt to extract project requirements."""
        try:
            analysis_prompt = f"""
Analyze this project request and extract structured information:

Request: "{request.prompt}"

Please provide a JSON response with the following structure:
{{
    "project_type": "web_app|api|desktop_app|data_science|mobile_app|cli_tool|library|game|automation|microservice",
    "framework": "react|angular|vue|flask|django|fastapi|express|nextjs|tkinter|pyqt|electron|jupyter|streamlit|dash|react_native|flutter|vanilla|custom",
    "language": "python|javascript|typescript|java|csharp|go|rust|php|ruby|swift|kotlin",
    "features": ["list", "of", "required", "features"],
    "complexity": "simple|medium|complex",
    "database": "sqlite|postgresql|mysql|mongodb|none",
    "authentication": true/false,
    "api_integration": true/false,
    "testing_required": true/false,
    "deployment_target": "local|cloud|docker|none",
    "ui_framework": "bootstrap|tailwind|material|custom|none",
    "project_name": "suggested_name",
    "description": "brief description of the project"
}}

Focus on extracting concrete technical requirements from the natural language description.
"""
            
            response = self.ai_integration.model.generate_content(analysis_prompt)
            
            # Parse JSON response
            try:
                # Extract JSON from response
                json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
                if json_match:
                    analysis = json.loads(json_match.group())
                else:
                    # Fallback parsing
                    analysis = self._fallback_analysis(request.prompt)
            except json.JSONDecodeError:
                analysis = self._fallback_analysis(request.prompt)
            
            # Apply user preferences
            if request.preferred_framework:
                analysis['framework'] = request.preferred_framework.value
            if request.preferred_language:
                analysis['language'] = request.preferred_language
            if request.project_name:
                analysis['project_name'] = request.project_name
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing project requirements: {e}")
            return self._fallback_analysis(request.prompt)
    
    def _fallback_analysis(self, prompt: str) -> Dict[str, Any]:
        """Fallback analysis when AI parsing fails."""
        # Simple keyword-based analysis
        prompt_lower = prompt.lower()
        
        # Detect project type
        if any(word in prompt_lower for word in ['web', 'website', 'webapp', 'frontend']):
            project_type = "web_app"
        elif any(word in prompt_lower for word in ['api', 'backend', 'server', 'rest']):
            project_type = "api"
        elif any(word in prompt_lower for word in ['desktop', 'gui', 'tkinter', 'pyqt']):
            project_type = "desktop_app"
        elif any(word in prompt_lower for word in ['data', 'analysis', 'jupyter', 'pandas']):
            project_type = "data_science"
        elif any(word in prompt_lower for word in ['cli', 'command', 'terminal']):
            project_type = "cli_tool"
        else:
            project_type = "web_app"  # Default
        
        # Detect framework
        if 'react' in prompt_lower:
            framework = "react"
        elif 'flask' in prompt_lower:
            framework = "flask"
        elif 'django' in prompt_lower:
            framework = "django"
        elif 'fastapi' in prompt_lower:
            framework = "fastapi"
        else:
            framework = "vanilla"
        
        # Detect language
        if any(word in prompt_lower for word in ['python', 'py']):
            language = "python"
        elif any(word in prompt_lower for word in ['javascript', 'js', 'node']):
            language = "javascript"
        elif any(word in prompt_lower for word in ['typescript', 'ts']):
            language = "typescript"
        else:
            language = "python"  # Default
        
        return {
            "project_type": project_type,
            "framework": framework,
            "language": language,
            "features": ["basic_functionality"],
            "complexity": "simple",
            "database": "none",
            "authentication": False,
            "api_integration": False,
            "testing_required": True,
            "deployment_target": "local",
            "ui_framework": "none",
            "project_name": "generated_project",
            "description": "Generated project based on user prompt"
        }
    
    def _generate_project_structure(self, analysis: Dict[str, Any], 
                                  request: ProjectGenerationRequest) -> ProjectStructure:
        """Generate the overall project structure."""
        try:
            project_type = ProjectType(analysis.get('project_type', 'web_app'))
            framework = Framework(analysis.get('framework', 'vanilla'))
            language = analysis.get('language', 'python')
            
            # Get base template
            template = self.project_templates.get(project_type.value, {})
            framework_config = self.framework_patterns.get(framework.value, {})
            
            # Determine directory structure
            directories = template.get('directories', [])
            if framework_config.get('directories'):
                directories.extend(framework_config['directories'])
            
            # Determine dependencies
            dependencies = {}
            if language == 'python':
                dependencies['pip'] = template.get('python_dependencies', [])
                if framework_config.get('python_dependencies'):
                    dependencies['pip'].extend(framework_config['python_dependencies'])
            elif language in ['javascript', 'typescript']:
                dependencies['npm'] = template.get('npm_dependencies', [])
                if framework_config.get('npm_dependencies'):
                    dependencies['npm'].extend(framework_config['npm_dependencies'])
            
            # Generate setup instructions
            setup_instructions = self._generate_setup_instructions(analysis, dependencies)
            
            return ProjectStructure(
                name=analysis.get('project_name', 'generated_project'),
                description=analysis.get('description', 'Generated project'),
                project_type=project_type,
                framework=framework,
                language=language,
                files=[],  # Will be populated by _generate_project_files
                directories=directories,
                dependencies=dependencies,
                setup_instructions=setup_instructions,
                estimated_complexity=analysis.get('complexity', 'simple')
            )
            
        except Exception as e:
            logger.error(f"Error generating project structure: {e}")
            raise
    
    def _generate_project_files(self, project_structure: ProjectStructure, 
                               analysis: Dict[str, Any]):
        """Generate individual files for the project."""
        try:
            # Generate files based on project type and framework
            file_generators = {
                ProjectType.WEB_APP: self._generate_web_app_files,
                ProjectType.API: self._generate_api_files,
                ProjectType.DESKTOP_APP: self._generate_desktop_app_files,
                ProjectType.DATA_SCIENCE: self._generate_data_science_files,
                ProjectType.CLI_TOOL: self._generate_cli_tool_files,
            }
            
            generator = file_generators.get(project_structure.project_type, self._generate_basic_files)
            generator(project_structure, analysis)
            
            # Always generate common files
            self._generate_common_files(project_structure, analysis)
            
        except Exception as e:
            logger.error(f"Error generating project files: {e}")
            raise
    
    def _generate_web_app_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate files for web applications."""
        if project_structure.framework == Framework.REACT:
            self._generate_react_files(project_structure, analysis)
        elif project_structure.framework == Framework.FLASK:
            self._generate_flask_files(project_structure, analysis)
        elif project_structure.framework == Framework.DJANGO:
            self._generate_django_files(project_structure, analysis)
        else:
            self._generate_vanilla_web_files(project_structure, analysis)
    
    def _generate_api_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate files for API projects."""
        if project_structure.framework == Framework.FASTAPI:
            self._generate_fastapi_files(project_structure, analysis)
        elif project_structure.framework == Framework.FLASK:
            self._generate_flask_api_files(project_structure, analysis)
        else:
            self._generate_basic_api_files(project_structure, analysis)
    
    def _generate_desktop_app_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate files for desktop applications."""
        if project_structure.framework == Framework.TKINTER:
            self._generate_tkinter_files(project_structure, analysis)
        else:
            self._generate_basic_desktop_files(project_structure, analysis)
    
    def _generate_data_science_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate files for data science projects."""
        self._generate_jupyter_files(project_structure, analysis)
    
    def _generate_cli_tool_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate files for CLI tools."""
        self._generate_basic_cli_files(project_structure, analysis)
    
    def _generate_basic_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate basic project files."""
        # Main application file
        if project_structure.language == 'python':
            main_content = self._generate_python_main(analysis)
            project_structure.files.append(ProjectFile(
                path="main.py",
                content=main_content,
                description="Main application entry point",
                file_type="python"
            ))
        elif project_structure.language in ['javascript', 'typescript']:
            main_content = self._generate_js_main(analysis)
            ext = 'ts' if project_structure.language == 'typescript' else 'js'
            project_structure.files.append(ProjectFile(
                path=f"main.{ext}",
                content=main_content,
                description="Main application entry point",
                file_type=project_structure.language
            ))
    
    def _load_project_templates(self) -> Dict[str, Any]:
        """Load project templates configuration."""
        # This would typically load from external files
        return {
            "web_app": {
                "directories": ["src", "public", "assets", "components"],
                "python_dependencies": ["flask", "jinja2"],
                "npm_dependencies": ["react", "react-dom"]
            },
            "api": {
                "directories": ["src", "tests", "docs"],
                "python_dependencies": ["fastapi", "uvicorn", "pydantic"],
                "npm_dependencies": ["express", "cors"]
            },
            "data_science": {
                "directories": ["data", "notebooks", "src", "reports"],
                "python_dependencies": ["pandas", "numpy", "matplotlib", "jupyter"]
            },
            "cli_tool": {
                "directories": ["src", "tests"],
                "python_dependencies": ["click", "rich"],
                "npm_dependencies": ["commander", "chalk"]
            }
        }
    
    def _load_framework_patterns(self) -> Dict[str, Any]:
        """Load framework-specific patterns."""
        return {
            "react": {
                "directories": ["src/components", "src/hooks", "src/utils", "public"],
                "npm_dependencies": ["react", "react-dom", "react-router-dom"]
            },
            "flask": {
                "directories": ["templates", "static", "app"],
                "python_dependencies": ["flask", "flask-sqlalchemy", "flask-migrate"]
            },
            "fastapi": {
                "directories": ["app", "app/routers", "app/models", "app/schemas"],
                "python_dependencies": ["fastapi", "uvicorn", "sqlalchemy", "pydantic"]
            }
        }

    def _generate_setup_instructions(self, analysis: Dict[str, Any],
                                   dependencies: Dict[str, List[str]]) -> List[str]:
        """Generate setup instructions for the project."""
        instructions = []
        language = analysis.get('language', 'python')

        if language == 'python' and 'pip' in dependencies:
            instructions.extend([
                "# Python Project Setup",
                "1. Create a virtual environment: python -m venv venv",
                "2. Activate virtual environment:",
                "   - Windows: venv\\Scripts\\activate",
                "   - macOS/Linux: source venv/bin/activate",
                "3. Install dependencies: pip install -r requirements.txt",
                "4. Run the application: python main.py"
            ])
        elif language in ['javascript', 'typescript'] and 'npm' in dependencies:
            instructions.extend([
                "# Node.js Project Setup",
                "1. Install Node.js (https://nodejs.org/)",
                "2. Install dependencies: npm install",
                "3. Start development server: npm start",
                "4. Build for production: npm run build"
            ])

        return instructions

    def _generate_common_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate common files for all projects."""
        # README.md
        readme_content = self._generate_readme(project_structure, analysis)
        project_structure.files.append(ProjectFile(
            path="README.md",
            content=readme_content,
            description="Project documentation",
            file_type="markdown"
        ))

        # .gitignore
        gitignore_content = self._generate_gitignore(project_structure)
        project_structure.files.append(ProjectFile(
            path=".gitignore",
            content=gitignore_content,
            description="Git ignore file",
            file_type="text"
        ))

        # Dependencies file
        if project_structure.language == 'python':
            deps = project_structure.dependencies.get('pip', [])
            requirements_content = '\n'.join(deps)
            project_structure.files.append(ProjectFile(
                path="requirements.txt",
                content=requirements_content,
                description="Python dependencies",
                file_type="text"
            ))
        elif project_structure.language in ['javascript', 'typescript']:
            package_json = self._generate_package_json(project_structure, analysis)
            project_structure.files.append(ProjectFile(
                path="package.json",
                content=package_json,
                description="Node.js package configuration",
                file_type="json"
            ))

    def _generate_readme(self, project_structure: ProjectStructure, analysis: Dict[str, Any]) -> str:
        """Generate README.md content."""
        return f"""# {project_structure.name}

{project_structure.description}

## Project Type
{project_structure.project_type.value.replace('_', ' ').title()}

## Technology Stack
- **Language**: {project_structure.language.title()}
- **Framework**: {project_structure.framework.value.replace('_', ' ').title()}
- **Complexity**: {project_structure.estimated_complexity.title()}

## Features
{chr(10).join(f"- {feature}" for feature in analysis.get('features', ['Basic functionality']))}

## Setup Instructions
{chr(10).join(project_structure.setup_instructions)}

## Project Structure
```
{project_structure.name}/
{chr(10).join(f"├── {directory}/" for directory in project_structure.directories)}
{chr(10).join(f"├── {file.path}" for file in project_structure.files)}
```

## Usage
[Add usage instructions here]

## Contributing
[Add contributing guidelines here]

## License
[Add license information here]
"""

    def _generate_gitignore(self, project_structure: ProjectStructure) -> str:
        """Generate .gitignore content."""
        common_ignores = [
            "# IDE and Editor files",
            ".vscode/",
            ".idea/",
            "*.swp",
            "*.swo",
            "*~",
            "",
            "# OS generated files",
            ".DS_Store",
            ".DS_Store?",
            "._*",
            ".Spotlight-V100",
            ".Trashes",
            "ehthumbs.db",
            "Thumbs.db",
            ""
        ]

        if project_structure.language == 'python':
            python_ignores = [
                "# Python",
                "__pycache__/",
                "*.py[cod]",
                "*$py.class",
                "*.so",
                ".Python",
                "build/",
                "develop-eggs/",
                "dist/",
                "downloads/",
                "eggs/",
                ".eggs/",
                "lib/",
                "lib64/",
                "parts/",
                "sdist/",
                "var/",
                "wheels/",
                "*.egg-info/",
                ".installed.cfg",
                "*.egg",
                "MANIFEST",
                "",
                "# Virtual environments",
                "venv/",
                "env/",
                "ENV/",
                ""
            ]
            common_ignores.extend(python_ignores)

        elif project_structure.language in ['javascript', 'typescript']:
            node_ignores = [
                "# Node.js",
                "node_modules/",
                "npm-debug.log*",
                "yarn-debug.log*",
                "yarn-error.log*",
                ".npm",
                ".eslintcache",
                "",
                "# Build outputs",
                "build/",
                "dist/",
                ".next/",
                "out/",
                ""
            ]
            common_ignores.extend(node_ignores)

        return '\n'.join(common_ignores)

    def _generate_package_json(self, project_structure: ProjectStructure, analysis: Dict[str, Any]) -> str:
        """Generate package.json content."""
        package_data = {
            "name": project_structure.name.lower().replace(' ', '-'),
            "version": "1.0.0",
            "description": project_structure.description,
            "main": "main.js" if project_structure.language == 'javascript' else "main.ts",
            "scripts": {
                "start": "node main.js" if project_structure.language == 'javascript' else "ts-node main.ts",
                "build": "tsc" if project_structure.language == 'typescript' else "echo 'No build step required'",
                "test": "jest",
                "dev": "nodemon main.js" if project_structure.language == 'javascript' else "ts-node-dev main.ts"
            },
            "keywords": analysis.get('features', []),
            "author": "",
            "license": "MIT",
            "dependencies": {},
            "devDependencies": {}
        }

        # Add dependencies
        deps = project_structure.dependencies.get('npm', [])
        for dep in deps:
            package_data["dependencies"][dep] = "^latest"

        # Add dev dependencies
        if project_structure.language == 'typescript':
            package_data["devDependencies"].update({
                "typescript": "^latest",
                "ts-node": "^latest",
                "ts-node-dev": "^latest",
                "@types/node": "^latest"
            })

        package_data["devDependencies"].update({
            "jest": "^latest",
            "nodemon": "^latest"
        })

        return json.dumps(package_data, indent=2)

    def _generate_python_main(self, analysis: Dict[str, Any]) -> str:
        """Generate main.py content."""
        return f'''"""
{analysis.get('project_name', 'Generated Project')}

{analysis.get('description', 'A generated Python project')}
"""

def main():
    """Main application entry point."""
    print("Welcome to {analysis.get('project_name', 'Generated Project')}!")

    # TODO: Implement your application logic here
    pass

if __name__ == "__main__":
    main()
'''

    def _generate_js_main(self, analysis: Dict[str, Any]) -> str:
        """Generate main.js/ts content."""
        return f'''/**
 * {analysis.get('project_name', 'Generated Project')}
 *
 * {analysis.get('description', 'A generated JavaScript/TypeScript project')}
 */

function main() {{
    console.log("Welcome to {analysis.get('project_name', 'Generated Project')}!");

    // TODO: Implement your application logic here
}}

// Run the application
main();
'''

    def _generate_react_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate React application files."""
        # App.jsx/tsx
        app_ext = 'tsx' if project_structure.language == 'typescript' else 'jsx'
        app_content = self._generate_react_app_component(analysis)
        project_structure.files.append(ProjectFile(
            path=f"src/App.{app_ext}",
            content=app_content,
            description="Main React application component",
            file_type="react"
        ))

        # index.js/ts
        index_ext = 'ts' if project_structure.language == 'typescript' else 'js'
        index_content = self._generate_react_index(analysis)
        project_structure.files.append(ProjectFile(
            path=f"src/index.{index_ext}",
            content=index_content,
            description="React application entry point",
            file_type="react"
        ))

        # index.html
        html_content = self._generate_react_html(analysis)
        project_structure.files.append(ProjectFile(
            path="public/index.html",
            content=html_content,
            description="HTML template",
            file_type="html"
        ))

        # CSS file
        css_content = self._generate_react_css()
        project_structure.files.append(ProjectFile(
            path="src/App.css",
            content=css_content,
            description="Application styles",
            file_type="css"
        ))

    def _generate_react_app_component(self, analysis: Dict[str, Any]) -> str:
        """Generate React App component."""
        return f'''import React from 'react';
import './App.css';

function App() {{
  return (
    <div className="App">
      <header className="App-header">
        <h1>{analysis.get('project_name', 'React App')}</h1>
        <p>{analysis.get('description', 'Welcome to your React application!')}</p>
        <div className="features">
          <h2>Features:</h2>
          <ul>
            {chr(10).join(f"            <li>{feature}</li>" for feature in analysis.get('features', ['Basic React setup']))}
          </ul>
        </div>
      </header>
    </div>
  );
}}

export default App;
'''

    def _generate_react_index(self, analysis: Dict[str, Any]) -> str:
        """Generate React index file."""
        return '''import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
'''

    def _generate_react_html(self, analysis: Dict[str, Any]) -> str:
        """Generate HTML template for React."""
        return f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{analysis.get('project_name', 'React App')}</title>
</head>
<body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
</body>
</html>
'''

    def _generate_react_css(self) -> str:
        """Generate CSS for React app."""
        return '''.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.features {
  margin-top: 2rem;
}

.features ul {
  list-style-type: none;
  padding: 0;
}

.features li {
  margin: 0.5rem 0;
  padding: 0.5rem;
  background-color: #61dafb;
  color: #282c34;
  border-radius: 4px;
}

h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}
'''

    def _generate_flask_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate Flask application files."""
        # app.py
        app_content = self._generate_flask_app(analysis)
        project_structure.files.append(ProjectFile(
            path="app.py",
            content=app_content,
            description="Flask application",
            file_type="python"
        ))

        # templates/index.html
        template_content = self._generate_flask_template(analysis)
        project_structure.files.append(ProjectFile(
            path="templates/index.html",
            content=template_content,
            description="HTML template",
            file_type="html"
        ))

        # static/style.css
        css_content = self._generate_flask_css()
        project_structure.files.append(ProjectFile(
            path="static/style.css",
            content=css_content,
            description="CSS styles",
            file_type="css"
        ))

    def _generate_flask_app(self, analysis: Dict[str, Any]) -> str:
        """Generate Flask application code."""
        return f'''"""
{analysis.get('project_name', 'Flask App')}

{analysis.get('description', 'A Flask web application')}
"""

from flask import Flask, render_template

app = Flask(__name__)

@app.route('/')
def index():
    """Home page."""
    return render_template('index.html',
                         project_name="{analysis.get('project_name', 'Flask App')}",
                         description="{analysis.get('description', 'Welcome to your Flask application!')}",
                         features={analysis.get('features', ['Basic Flask setup'])})

@app.route('/about')
def about():
    """About page."""
    return render_template('about.html')

if __name__ == '__main__':
    app.run(debug=True)
'''

    def _generate_flask_template(self, analysis: Dict[str, Any]) -> str:
        """Generate Flask HTML template."""
        return f'''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{{{ project_name }}}}</title>
    <link rel="stylesheet" href="{{{{ url_for('static', filename='style.css') }}}}">
</head>
<body>
    <div class="container">
        <header>
            <h1>{{{{ project_name }}}}</h1>
            <p>{{{{ description }}}}</p>
        </header>

        <main>
            <section class="features">
                <h2>Features:</h2>
                <ul>
                    {{% for feature in features %}}
                    <li>{{{{ feature }}}}</li>
                    {{% endfor %}}
                </ul>
            </section>

            <section class="actions">
                <a href="/about" class="btn">About</a>
            </section>
        </main>
    </div>
</body>
</html>
'''

    def _generate_flask_css(self) -> str:
        """Generate CSS for Flask app."""
        return '''body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    min-height: 100vh;
}

header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #007bff;
}

h1 {
    color: #333;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

h2 {
    color: #555;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.features ul {
    list-style-type: none;
    padding: 0;
}

.features li {
    margin: 0.5rem 0;
    padding: 0.75rem;
    background-color: #007bff;
    color: white;
    border-radius: 4px;
}

.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #0056b3;
}

.actions {
    text-align: center;
    margin-top: 2rem;
}
'''

    def _generate_fastapi_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate FastAPI application files."""
        # main.py
        main_content = self._generate_fastapi_main(analysis)
        project_structure.files.append(ProjectFile(
            path="main.py",
            content=main_content,
            description="FastAPI application",
            file_type="python"
        ))

        # models.py
        models_content = self._generate_fastapi_models(analysis)
        project_structure.files.append(ProjectFile(
            path="app/models.py",
            content=models_content,
            description="Data models",
            file_type="python"
        ))

        # schemas.py
        schemas_content = self._generate_fastapi_schemas(analysis)
        project_structure.files.append(ProjectFile(
            path="app/schemas.py",
            content=schemas_content,
            description="Pydantic schemas",
            file_type="python"
        ))

    def _generate_fastapi_main(self, analysis: Dict[str, Any]) -> str:
        """Generate FastAPI main application."""
        return f'''"""
{analysis.get('project_name', 'FastAPI App')}

{analysis.get('description', 'A FastAPI application')}
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(
    title="{analysis.get('project_name', 'FastAPI App')}",
    description="{analysis.get('description', 'A FastAPI application')}",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint."""
    return {{
        "message": "Welcome to {analysis.get('project_name', 'FastAPI App')}!",
        "description": "{analysis.get('description', 'A FastAPI application')}",
        "features": {analysis.get('features', ['Basic FastAPI setup'])}
    }}

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {{"status": "healthy"}}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
'''

    def _generate_fastapi_models(self, analysis: Dict[str, Any]) -> str:
        """Generate FastAPI models."""
        return '''"""
Database models for the application.
"""

from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()

class Item(Base):
    """Example item model."""
    __tablename__ = "items"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
'''

    def _generate_fastapi_schemas(self, analysis: Dict[str, Any]) -> str:
        """Generate FastAPI Pydantic schemas."""
        return '''"""
Pydantic schemas for request/response models.
"""

from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class ItemBase(BaseModel):
    """Base item schema."""
    name: str
    description: Optional[str] = None

class ItemCreate(ItemBase):
    """Schema for creating items."""
    pass

class ItemUpdate(ItemBase):
    """Schema for updating items."""
    name: Optional[str] = None

class Item(ItemBase):
    """Schema for item responses."""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
'''

    def _generate_tkinter_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate Tkinter desktop application files."""
        # main.py
        main_content = self._generate_tkinter_main(analysis)
        project_structure.files.append(ProjectFile(
            path="main.py",
            content=main_content,
            description="Tkinter desktop application",
            file_type="python"
        ))

    def _generate_tkinter_main(self, analysis: Dict[str, Any]) -> str:
        """Generate Tkinter application."""
        return f'''"""
{analysis.get('project_name', 'Desktop App')}

{analysis.get('description', 'A Tkinter desktop application')}
"""

import tkinter as tk
from tkinter import ttk, messagebox

class MainApplication:
    """Main application class."""

    def __init__(self, root):
        self.root = root
        self.root.title("{analysis.get('project_name', 'Desktop App')}")
        self.root.geometry("800x600")

        self.create_widgets()

    def create_widgets(self):
        """Create and layout widgets."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Title
        title_label = ttk.Label(main_frame, text="{analysis.get('project_name', 'Desktop App')}",
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        # Description
        desc_label = ttk.Label(main_frame, text="{analysis.get('description', 'Welcome to your desktop application!')}")
        desc_label.grid(row=1, column=0, columnspan=2, pady=(0, 20))

        # Features list
        features_label = ttk.Label(main_frame, text="Features:", font=("Arial", 12, "bold"))
        features_label.grid(row=2, column=0, sticky=tk.W, pady=(0, 5))

        features_text = tk.Text(main_frame, height=10, width=50)
        features_text.grid(row=3, column=0, columnspan=2, pady=(0, 20))

        # Add features to text widget
        features = {analysis.get('features', ['Basic desktop application'])}
        for i, feature in enumerate(features, 1):
            features_text.insert(tk.END, f"{{i}}. {{feature}}\\n")

        features_text.config(state=tk.DISABLED)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=10)

        ttk.Button(button_frame, text="About", command=self.show_about).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Exit", command=self.root.quit).pack(side=tk.LEFT, padx=5)

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)

    def show_about(self):
        """Show about dialog."""
        messagebox.showinfo("About",
                          f"{analysis.get('project_name', 'Desktop App')}\\n\\n"
                          f"{analysis.get('description', 'A Tkinter desktop application')}")

def main():
    """Main function."""
    root = tk.Tk()
    app = MainApplication(root)
    root.mainloop()

if __name__ == "__main__":
    main()
'''

    def _generate_jupyter_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate Jupyter notebook files for data science projects."""
        # Main analysis notebook
        notebook_content = self._generate_jupyter_notebook(analysis)
        project_structure.files.append(ProjectFile(
            path="notebooks/analysis.ipynb",
            content=notebook_content,
            description="Main analysis notebook",
            file_type="jupyter"
        ))

        # Data processing script
        data_script = self._generate_data_processing_script(analysis)
        project_structure.files.append(ProjectFile(
            path="src/data_processing.py",
            content=data_script,
            description="Data processing utilities",
            file_type="python"
        ))

    def _generate_jupyter_notebook(self, analysis: Dict[str, Any]) -> str:
        """Generate Jupyter notebook content."""
        notebook = {
            "cells": [
                {
                    "cell_type": "markdown",
                    "metadata": {},
                    "source": [
                        f"# {analysis.get('project_name', 'Data Science Project')}\\n",
                        "\\n",
                        f"{analysis.get('description', 'A data science analysis project')}\\n"
                    ]
                },
                {
                    "cell_type": "code",
                    "execution_count": None,
                    "metadata": {},
                    "outputs": [],
                    "source": [
                        "# Import required libraries\\n",
                        "import pandas as pd\\n",
                        "import numpy as np\\n",
                        "import matplotlib.pyplot as plt\\n",
                        "import seaborn as sns\\n",
                        "\\n",
                        "# Set up plotting\\n",
                        "plt.style.use('default')\\n",
                        "sns.set_palette('husl')\\n",
                        "%matplotlib inline"
                    ]
                },
                {
                    "cell_type": "markdown",
                    "metadata": {},
                    "source": [
                        "## Data Loading\\n",
                        "\\n",
                        "Load and explore the dataset."
                    ]
                },
                {
                    "cell_type": "code",
                    "execution_count": None,
                    "metadata": {},
                    "outputs": [],
                    "source": [
                        "# Load data\\n",
                        "# df = pd.read_csv('data/dataset.csv')\\n",
                        "\\n",
                        "# Display basic information\\n",
                        "# print(df.info())\\n",
                        "# print(df.head())"
                    ]
                }
            ],
            "metadata": {
                "kernelspec": {
                    "display_name": "Python 3",
                    "language": "python",
                    "name": "python3"
                },
                "language_info": {
                    "name": "python",
                    "version": "3.8.0"
                }
            },
            "nbformat": 4,
            "nbformat_minor": 4
        }
        return json.dumps(notebook, indent=2)

    def _generate_data_processing_script(self, analysis: Dict[str, Any]) -> str:
        """Generate data processing utilities."""
        return f'''"""
Data processing utilities for {analysis.get('project_name', 'Data Science Project')}.
"""

import pandas as pd
import numpy as np
from typing import Optional, List, Dict, Any

def load_data(file_path: str) -> pd.DataFrame:
    """Load data from file."""
    try:
        if file_path.endswith('.csv'):
            return pd.read_csv(file_path)
        elif file_path.endswith('.json'):
            return pd.read_json(file_path)
        elif file_path.endswith('.xlsx'):
            return pd.read_excel(file_path)
        else:
            raise ValueError(f"Unsupported file format: {{file_path}}")
    except Exception as e:
        print(f"Error loading data: {{e}}")
        return pd.DataFrame()

def clean_data(df: pd.DataFrame) -> pd.DataFrame:
    """Clean and preprocess data."""
    # Remove duplicates
    df = df.drop_duplicates()

    # Handle missing values
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    df[numeric_columns] = df[numeric_columns].fillna(df[numeric_columns].median())

    categorical_columns = df.select_dtypes(include=['object']).columns
    df[categorical_columns] = df[categorical_columns].fillna(df[categorical_columns].mode().iloc[0])

    return df

def get_data_summary(df: pd.DataFrame) -> Dict[str, Any]:
    """Get summary statistics for the dataset."""
    return {{
        'shape': df.shape,
        'columns': df.columns.tolist(),
        'dtypes': df.dtypes.to_dict(),
        'missing_values': df.isnull().sum().to_dict(),
        'numeric_summary': df.describe().to_dict() if len(df.select_dtypes(include=[np.number]).columns) > 0 else {{}}
    }}
'''

    def _generate_basic_cli_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate basic CLI tool files."""
        # main.py with click
        cli_content = self._generate_cli_main(analysis)
        project_structure.files.append(ProjectFile(
            path="main.py",
            content=cli_content,
            description="CLI application",
            file_type="python"
        ))

    def _generate_cli_main(self, analysis: Dict[str, Any]) -> str:
        """Generate CLI application with click."""
        return f'''"""
{analysis.get('project_name', 'CLI Tool')}

{analysis.get('description', 'A command-line interface tool')}
"""

import click
from rich.console import Console
from rich.table import Table

console = Console()

@click.group()
@click.version_option(version="1.0.0")
def cli():
    """
    {analysis.get('project_name', 'CLI Tool')}

    {analysis.get('description', 'A command-line interface tool')}
    """
    pass

@cli.command()
@click.option('--name', default='World', help='Name to greet')
def hello(name):
    """Say hello to someone."""
    console.print(f"Hello, {{name}}!", style="bold green")

@cli.command()
def features():
    """Show available features."""
    table = Table(title="Available Features")
    table.add_column("Feature", style="cyan")
    table.add_column("Description", style="white")

    features = {analysis.get('features', ['Basic CLI functionality'])}
    for i, feature in enumerate(features, 1):
        table.add_row(f"Feature {{i}}", feature)

    console.print(table)

@cli.command()
def info():
    """Show project information."""
    console.print(f"[bold]{analysis.get('project_name', 'CLI Tool')}[/bold]")
    console.print(f"{analysis.get('description', 'A command-line interface tool')}")
    console.print(f"Version: 1.0.0")

if __name__ == '__main__':
    cli()
'''

    # Placeholder methods for other frameworks
    def _generate_django_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate Django application files."""
        # This would be implemented with Django-specific structure
        pass

    def _generate_flask_api_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate Flask API files."""
        # Similar to Flask but API-focused
        self._generate_flask_files(project_structure, analysis)

    def _generate_basic_api_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate basic API files."""
        # Basic API implementation
        pass

    def _generate_basic_desktop_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate basic desktop application files."""
        # Basic desktop app
        pass

    def _generate_vanilla_web_files(self, project_structure: ProjectStructure, analysis: Dict[str, Any]):
        """Generate vanilla HTML/CSS/JS files."""
        # Basic web files
        pass
