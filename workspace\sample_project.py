"""Sample Python project for testing the AI Code Assistant."""

import os
import sys
from typing import List, Dict, Optional

class Calculator:
    """A simple calculator class."""
    
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def subtract(self, a, b):
        result = a - b
        self.history.append(f"{a} - {b} = {result}")
        return result
    
    def multiply(self, a, b):
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result
    
    def divide(self, a, b):
        if b == 0:
            raise ValueError("Cannot divide by zero")
        result = a / b
        self.history.append(f"{a} / {b} = {result}")
        return result
    
    def get_history(self):
        return self.history

def fibonacci(n):
    """Calculate fibonacci number (inefficient implementation)."""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibon<PERSON><PERSON>(n-2)

def process_data(data):
    """Process a list of numbers."""
    # TODO: Add error handling
    # TODO: Add input validation
    result = []
    for item in data:
        if item > 0:
            result.append(item * 2)
        else:
            result.append(item)
    return result

def main():
    """Main function with some issues."""
    calc = Calculator()
    
    # Some calculations
    print(calc.add(5, 3))
    print(calc.subtract(10, 4))
    print(calc.multiply(3, 7))
    
    # This could cause an error
    try:
        print(calc.divide(10, 0))
    except ValueError as e:
        print(f"Error: {e}")
    
    # Inefficient fibonacci
    print(f"Fibonacci(10): {fibonacci(10)}")
    
    # Process some data
    numbers = [1, -2, 3, -4, 5]
    processed = process_data(numbers)
    print(f"Processed: {processed}")
    
    # Show history
    print("Calculation history:")
    for entry in calc.get_history():
        print(f"  {entry}")

if __name__ == "__main__":
    main()
