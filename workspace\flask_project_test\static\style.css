body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    min-height: 100vh;
}

header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #007bff;
}

h1 {
    color: #333;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

h2 {
    color: #555;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.features ul {
    list-style-type: none;
    padding: 0;
}

.features li {
    margin: 0.5rem 0;
    padding: 0.75rem;
    background-color: #007bff;
    color: white;
    border-radius: 4px;
}

.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #0056b3;
}

.actions {
    text-align: center;
    margin-top: 2rem;
}
