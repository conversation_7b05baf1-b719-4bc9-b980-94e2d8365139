# AI Code Assistant

An intelligent code editor/assistant powered by Google Gemini 1.5 Flash, inspired by tools like Cursor and Codex.

## Features

- 🤖 **AI-Powered Code Analysis**: Uses Google Gemini 1.5 Flash for intelligent code suggestions
- 📁 **Full File Access**: Read, edit, and create files with proper security boundaries
- 🔄 **Change Management**: Review and approve AI suggestions with diff visualization
- 🛡️ **Security First**: Configurable permissions and workspace boundaries
- 🖥️ **Dual Interface**: Both CLI and web-based interfaces available
- 📝 **Multi-Language Support**: Works with Python, JavaScript, HTML, CSS, TypeScript, JSON, and more

## Quick Start

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure API Key**:
   - Copy `.env.example` to `.env`
   - Add your Gemini API key to the `.env` file

3. **Run the Application**:
   ```bash
   # Web interface (default)
   python main.py --mode web
   
   # CLI interface
   python main.py --mode cli
   
   # Custom workspace
   python main.py --workspace /path/to/your/project
   ```

## Configuration

Edit the `.env` file to customize:

- `GEMINI_API_KEY`: Your Google Gemini API key
- `WORKSPACE_ROOT`: Root directory for file operations
- `ALLOWED_EXTENSIONS`: File types the assistant can work with
- Security settings for file access restrictions

## Security

The assistant operates within configured boundaries:
- Only accesses files within the specified workspace
- Respects file extension restrictions
- Prevents access to system directories
- Requires user approval for all changes

## Usage

### Web Interface
1. Navigate to `http://localhost:8501` after starting
2. Browse and select files to analyze
3. Review AI suggestions with diff visualization
4. Accept or reject changes with one click

### CLI Interface
- Interactive command-line interface
- File browsing and editing capabilities
- Real-time AI suggestions and approvals

## Project Structure

```
ai-code-assistant/
├── main.py              # Application entry point
├── config.py            # Configuration management
├── core/                # Core functionality
│   ├── file_manager.py  # File operations
│   ├── ai_integration.py # Gemini API integration
│   └── diff_manager.py  # Change management
├── interfaces/          # User interfaces
│   ├── web_interface.py # Streamlit web UI
│   └── cli_interface.py # Command-line UI
├── tests/              # Test suite
└── workspace/          # Default workspace directory
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

MIT License - see LICENSE file for details.
