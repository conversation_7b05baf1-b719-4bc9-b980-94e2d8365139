"""OpenAI ChatGPT integration for AI Code Assistant."""

import logging
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import time

from config import Config

logger = logging.getLogger(__name__)

@dataclass
class OpenAIResponse:
    """Response from OpenAI API."""
    text: str
    model: str
    usage: Dict[str, Any]
    finish_reason: str

class OpenAIIntegration:
    """Integration with OpenAI ChatGPT API for code analysis and generation."""
    
    def __init__(self):
        """Initialize OpenAI integration."""
        try:
            import openai
            self.client = openai.OpenAI(api_key=Config.OPENAI_API_KEY)
            self.model = Config.OPENAI_MODEL
            
            # Test the connection
            self._test_connection()
            
            logger.info(f"OpenAI integration initialized successfully with model: {self.model}")
            
        except ImportError:
            raise ImportError("OpenAI package not installed. Run: pip install openai")
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI integration: {e}")
            raise
    
    def _test_connection(self):
        """Test OpenAI API connection."""
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            logger.info("OpenAI API connection test successful")
        except Exception as e:
            logger.error(f"OpenAI API connection test failed: {e}")
            raise
    
    def generate_content(self, prompt: str, max_tokens: int = 4000, temperature: float = 0.1) -> OpenAIResponse:
        """Generate content using OpenAI ChatGPT."""
        try:
            start_time = time.time()
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system", 
                        "content": "You are an expert software developer and code analyst. Provide precise, high-quality code and technical analysis."
                    },
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=0.9
            )
            
            duration = time.time() - start_time
            
            result = OpenAIResponse(
                text=response.choices[0].message.content,
                model=response.model,
                usage=response.usage.model_dump() if response.usage else {},
                finish_reason=response.choices[0].finish_reason
            )
            
            logger.info(f"OpenAI API call completed in {duration:.2f}s, tokens: {result.usage.get('total_tokens', 0)}")
            return result
            
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            raise
    
    def analyze_code(self, code: str, file_path: str, analysis_type: str) -> Dict[str, Any]:
        """Analyze code using OpenAI ChatGPT."""
        try:
            prompt = self._build_analysis_prompt(code, file_path, analysis_type)
            response = self.generate_content(prompt, max_tokens=3000)
            
            # Try to parse JSON response
            try:
                # Look for JSON in the response
                import re
                json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
                if json_match:
                    analysis = json.loads(json_match.group())
                else:
                    # Fallback to structured parsing
                    analysis = self._parse_analysis_response(response.text)
            except json.JSONDecodeError:
                analysis = self._parse_analysis_response(response.text)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Code analysis failed: {e}")
            raise
    
    def _build_analysis_prompt(self, code: str, file_path: str, analysis_type: str) -> str:
        """Build analysis prompt for OpenAI."""
        base_prompt = f"""
Analyze the following {file_path} code for {analysis_type}:

```
{code}
```

Please provide a detailed analysis in JSON format with the following structure:
{{
    "summary": "Brief summary of the analysis",
    "suggestions": [
        {{
            "type": "suggestion_type",
            "title": "Brief title",
            "description": "Detailed description",
            "code_change": "Suggested code change",
            "line_number": number,
            "confidence": 0.0-1.0,
            "reasoning": "Why this change is recommended"
        }}
    ],
    "overall_quality": "poor|fair|good|excellent",
    "complexity": "low|medium|high",
    "maintainability": "poor|fair|good|excellent"
}}
"""
        
        if analysis_type == "review":
            return base_prompt + """
Focus on:
- Code quality and best practices
- Potential bugs and issues
- Performance improvements
- Security vulnerabilities
- Code style and formatting
"""
        elif analysis_type == "optimize":
            return base_prompt + """
Focus on:
- Performance optimizations
- Memory usage improvements
- Algorithm efficiency
- Database query optimization
- Caching opportunities
"""
        elif analysis_type == "debug":
            return base_prompt + """
Focus on:
- Potential bugs and errors
- Logic issues
- Exception handling
- Edge cases
- Runtime errors
"""
        elif analysis_type == "refactor":
            return base_prompt + """
Focus on:
- Code structure improvements
- Design pattern applications
- Code duplication removal
- Function/class organization
- Readability enhancements
"""
        else:
            return base_prompt
    
    def _parse_analysis_response(self, response_text: str) -> Dict[str, Any]:
        """Parse analysis response when JSON parsing fails."""
        # Fallback parsing logic
        return {
            "summary": "Analysis completed using OpenAI ChatGPT",
            "suggestions": [
                {
                    "type": "general",
                    "title": "AI Analysis Result",
                    "description": response_text[:500] + "..." if len(response_text) > 500 else response_text,
                    "code_change": "",
                    "line_number": 1,
                    "confidence": 0.8,
                    "reasoning": "Generated by OpenAI ChatGPT"
                }
            ],
            "overall_quality": "good",
            "complexity": "medium",
            "maintainability": "good"
        }
    
    def generate_project_structure(self, prompt: str) -> Dict[str, Any]:
        """Generate project structure using OpenAI ChatGPT."""
        try:
            structure_prompt = f"""
Create a complete project structure for: {prompt}

Provide a detailed JSON response with the following structure:
{{
    "project_name": "suggested_name",
    "description": "project description",
    "project_type": "web_app|api|desktop_app|data_science|mobile_app|cli_tool|library",
    "framework": "react|angular|vue|flask|django|fastapi|express|nextjs|vanilla",
    "language": "python|javascript|typescript|java|csharp|go|rust",
    "files": [
        {{
            "path": "relative/path/to/file.ext",
            "content": "complete file content",
            "description": "what this file does"
        }}
    ],
    "directories": ["dir1", "dir2"],
    "dependencies": {{
        "pip": ["package1", "package2"],
        "npm": ["package1", "package2"]
    }},
    "setup_instructions": ["step1", "step2"],
    "features": ["feature1", "feature2"]
}}

Generate complete, working code for all files. Focus on best practices and modern development patterns.
"""
            
            response = self.generate_content(structure_prompt, max_tokens=4000, temperature=0.2)
            
            # Parse JSON response
            try:
                import re
                json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    raise ValueError("No JSON found in response")
            except (json.JSONDecodeError, ValueError):
                # Fallback structure
                return self._create_fallback_structure(prompt)
                
        except Exception as e:
            logger.error(f"Project structure generation failed: {e}")
            return self._create_fallback_structure(prompt)
    
    def _create_fallback_structure(self, prompt: str) -> Dict[str, Any]:
        """Create fallback project structure."""
        return {
            "project_name": "generated_project",
            "description": f"Project generated from: {prompt}",
            "project_type": "web_app",
            "framework": "vanilla",
            "language": "python",
            "files": [
                {
                    "path": "main.py",
                    "content": f'"""\n{prompt}\n"""\n\nprint("Hello, World!")\n',
                    "description": "Main application file"
                },
                {
                    "path": "README.md",
                    "content": f"# Generated Project\n\n{prompt}\n\n## Setup\n\n```bash\npython main.py\n```\n",
                    "description": "Project documentation"
                }
            ],
            "directories": ["src", "tests"],
            "dependencies": {"pip": [], "npm": []},
            "setup_instructions": ["Run: python main.py"],
            "features": ["basic_functionality"]
        }
